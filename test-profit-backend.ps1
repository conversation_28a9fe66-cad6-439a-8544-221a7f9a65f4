# First login to get token
$loginBody = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-<PERSON>son

try {
    Write-Host "=== Testing Profit Backend API ==="
    
    # Login
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json" -UseBasicParsing
    $loginData = $loginResponse.Content | ConvertFrom-Json
    $token = $loginData.token
    
    Write-Host "✅ Login successful!"
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # Test 1: Get all profit data
    Write-Host "`n--- Test 1: Get All Profit Data ---"
    $profitResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Status: $($profitResponse.StatusCode)"
    Write-Host "Response: $($profitResponse.Content)"
    
    # Test 2: Get profit summary
    Write-Host "`n--- Test 2: Get Profit Summary ---"
    $summaryResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit/summary" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Status: $($summaryResponse.StatusCode)"
    Write-Host "Response: $($summaryResponse.Content)"
    
    # Test 3: Get specific event profit (using event ID 1)
    Write-Host "`n--- Test 3: Get Event Profit Details ---"
    $eventProfitResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit/1" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Status: $($eventProfitResponse.StatusCode)"
    Write-Host "Response: $($eventProfitResponse.Content)"
    
    # Test 4: Add expense to event
    Write-Host "`n--- Test 4: Add Expense to Event ---"
    $expenseBody = @{
        category = "Catering"
        description = "Food and beverages for wedding"
        amount = 500000
        expense_date = "2024-12-10"
        vendor_id = 2
    } | ConvertTo-Json
    
    $addExpenseResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/events/1/expenses" -Method POST -Body $expenseBody -Headers $headers -UseBasicParsing
    Write-Host "Status: $($addExpenseResponse.StatusCode)"
    Write-Host "Response: $($addExpenseResponse.Content)"
    
    # Test 5: Update event budget
    Write-Host "`n--- Test 5: Update Event Budget ---"
    $budgetBody = @{
        budget = 20000000
    } | ConvertTo-Json
    
    $budgetResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/events/1/budget" -Method PUT -Body $budgetBody -Headers $headers -UseBasicParsing
    Write-Host "Status: $($budgetResponse.StatusCode)"
    Write-Host "Response: $($budgetResponse.Content)"
    
    Write-Host "`n✅ All tests completed successfully!"
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status: $($_.Exception.Response.StatusCode)"
        $errorContent = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorContent)
        Write-Host "Error Response: $($reader.ReadToEnd())"
    }
}
