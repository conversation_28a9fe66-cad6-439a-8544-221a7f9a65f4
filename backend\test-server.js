const express = require("express")
const mysql = require("mysql2/promise")
require("dotenv").config()

const app = express()
const PORT = process.env.PORT || 3001

// Test database connection
async function testDatabase() {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "root",
      password: process.env.DB_PASSWORD || "",
      database: "otantik_ems",
    })
    
    console.log("✅ Database connection successful!")
    
    const [rows] = await connection.execute("SELECT COUNT(*) as count FROM users")
    console.log(`✅ Found ${rows[0].count} users in database`)
    
    await connection.end()
  } catch (error) {
    console.error("❌ Database connection failed:", error.message)
  }
}

// Basic route
app.get("/", (req, res) => {
  res.json({ message: "OtantikEMS Backend Server is running!" })
})

app.get("/api/test", (req, res) => {
  res.json({ message: "API is working!", timestamp: new Date().toISOString() })
})

// Start server
app.listen(PORT, async () => {
  console.log(`🚀 Server running on port ${PORT}`)
  await testDatabase()
})
