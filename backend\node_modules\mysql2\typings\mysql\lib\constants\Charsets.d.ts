interface Charsets {
  BIG5_CHINESE_CI: number;
  LATIN2_CZECH_CS: number;
  DEC8_SWEDISH_CI: number;
  CP850_GENERAL_CI: number;
  LATIN1_GERMAN1_CI: number;
  HP8_ENGLISH_CI: number;
  KOI8R_GENERAL_CI: number;
  LATIN1_SWEDISH_CI: number;
  LATIN2_GENERAL_CI: number;
  SWE7_SWEDISH_CI: number;
  ASCII_GENERAL_CI: number;
  UJIS_JAPANESE_CI: number;
  SJIS_JAPANESE_CI: number;
  CP1251_BULGARIAN_CI: number;
  LATIN1_DANISH_CI: number;
  HEBREW_GENERAL_CI: number;
  TIS620_THAI_CI: number;
  EUCKR_KOREAN_CI: number;
  LATIN7_ESTONIAN_CS: number;
  LATIN2_HUNGARIAN_CI: number;
  KOI8U_GENERAL_CI: number;
  CP1251_UKRAINIAN_CI: number;
  GB2312_CHINESE_CI: number;
  GREEK_GENERAL_CI: number;
  CP1250_GENERAL_CI: number;
  LATIN2_CROATIAN_CI: number;
  GBK_CHINESE_CI: number;
  CP1257_LITHUANIAN_CI: number;
  LATIN5_TURKISH_CI: number;
  LATIN1_GERMAN2_CI: number;
  ARMSCII8_GENERAL_CI: number;
  UTF8_GENERAL_CI: number;
  CP1250_CZECH_CS: number;
  UCS2_GENERAL_CI: number;
  CP866_GENERAL_CI: number;
  KEYBCS2_GENERAL_CI: number;
  MACCE_GENERAL_CI: number;
  MACROMAN_GENERAL_CI: number;
  CP852_GENERAL_CI: number;
  LATIN7_GENERAL_CI: number;
  LATIN7_GENERAL_CS: number;
  MACCE_BIN: number;
  CP1250_CROATIAN_CI: number;
  UTF8MB4_GENERAL_CI: number;
  UTF8MB4_BIN: number;
  LATIN1_BIN: number;
  LATIN1_GENERAL_CI: number;
  LATIN1_GENERAL_CS: number;
  CP1251_BIN: number;
  CP1251_GENERAL_CI: number;
  CP1251_GENERAL_CS: number;
  MACROMAN_BIN: number;
  UTF16_GENERAL_CI: number;
  UTF16_BIN: number;
  UTF16LE_GENERAL_CI: number;
  CP1256_GENERAL_CI: number;
  CP1257_BIN: number;
  CP1257_GENERAL_CI: number;
  UTF32_GENERAL_CI: number;
  UTF32_BIN: number;
  UTF16LE_BIN: number;
  BINARY: number;
  ARMSCII8_BIN: number;
  ASCII_BIN: number;
  CP1250_BIN: number;
  CP1256_BIN: number;
  CP866_BIN: number;
  DEC8_BIN: number;
  GREEK_BIN: number;
  HEBREW_BIN: number;
  HP8_BIN: number;
  KEYBCS2_BIN: number;
  KOI8R_BIN: number;
  KOI8U_BIN: number;
  UTF8_TOLOWER_CI: number;
  LATIN2_BIN: number;
  LATIN5_BIN: number;
  LATIN7_BIN: number;
  CP850_BIN: number;
  CP852_BIN: number;
  SWE7_BIN: number;
  UTF8_BIN: number;
  BIG5_BIN: number;
  EUCKR_BIN: number;
  GB2312_BIN: number;
  GBK_BIN: number;
  SJIS_BIN: number;
  TIS620_BIN: number;
  UCS2_BIN: number;
  UJIS_BIN: number;
  GEOSTD8_GENERAL_CI: number;
  GEOSTD8_BIN: number;
  LATIN1_SPANISH_CI: number;
  CP932_JAPANESE_CI: number;
  CP932_BIN: number;
  EUCJPMS_JAPANESE_CI: number;
  EUCJPMS_BIN: number;
  CP1250_POLISH_CI: number;
  UTF16_UNICODE_CI: number;
  UTF16_ICELANDIC_CI: number;
  UTF16_LATVIAN_CI: number;
  UTF16_ROMANIAN_CI: number;
  UTF16_SLOVENIAN_CI: number;
  UTF16_POLISH_CI: number;
  UTF16_ESTONIAN_CI: number;
  UTF16_SPANISH_CI: number;
  UTF16_SWEDISH_CI: number;
  UTF16_TURKISH_CI: number;
  UTF16_CZECH_CI: number;
  UTF16_DANISH_CI: number;
  UTF16_LITHUANIAN_CI: number;
  UTF16_SLOVAK_CI: number;
  UTF16_SPANISH2_CI: number;
  UTF16_ROMAN_CI: number;
  UTF16_PERSIAN_CI: number;
  UTF16_ESPERANTO_CI: number;
  UTF16_HUNGARIAN_CI: number;
  UTF16_SINHALA_CI: number;
  UTF16_GERMAN2_CI: number;
  UTF16_CROATIAN_CI: number;
  UTF16_UNICODE_520_CI: number;
  UTF16_VIETNAMESE_CI: number;
  UCS2_UNICODE_CI: number;
  UCS2_ICELANDIC_CI: number;
  UCS2_LATVIAN_CI: number;
  UCS2_ROMANIAN_CI: number;
  UCS2_SLOVENIAN_CI: number;
  UCS2_POLISH_CI: number;
  UCS2_ESTONIAN_CI: number;
  UCS2_SPANISH_CI: number;
  UCS2_SWEDISH_CI: number;
  UCS2_TURKISH_CI: number;
  UCS2_CZECH_CI: number;
  UCS2_DANISH_CI: number;
  UCS2_LITHUANIAN_CI: number;
  UCS2_SLOVAK_CI: number;
  UCS2_SPANISH2_CI: number;
  UCS2_ROMAN_CI: number;
  UCS2_PERSIAN_CI: number;
  UCS2_ESPERANTO_CI: number;
  UCS2_HUNGARIAN_CI: number;
  UCS2_SINHALA_CI: number;
  UCS2_GERMAN2_CI: number;
  UCS2_CROATIAN_CI: number;
  UCS2_UNICODE_520_CI: number;
  UCS2_VIETNAMESE_CI: number;
  UCS2_GENERAL_MYSQL500_CI: number;
  UTF32_UNICODE_CI: number;
  UTF32_ICELANDIC_CI: number;
  UTF32_LATVIAN_CI: number;
  UTF32_ROMANIAN_CI: number;
  UTF32_SLOVENIAN_CI: number;
  UTF32_POLISH_CI: number;
  UTF32_ESTONIAN_CI: number;
  UTF32_SPANISH_CI: number;
  UTF32_SWEDISH_CI: number;
  UTF32_TURKISH_CI: number;
  UTF32_CZECH_CI: number;
  UTF32_DANISH_CI: number;
  UTF32_LITHUANIAN_CI: number;
  UTF32_SLOVAK_CI: number;
  UTF32_SPANISH2_CI: number;
  UTF32_ROMAN_CI: number;
  UTF32_PERSIAN_CI: number;
  UTF32_ESPERANTO_CI: number;
  UTF32_HUNGARIAN_CI: number;
  UTF32_SINHALA_CI: number;
  UTF32_GERMAN2_CI: number;
  UTF32_CROATIAN_CI: number;
  UTF32_UNICODE_520_CI: number;
  UTF32_VIETNAMESE_CI: number;
  UTF8_UNICODE_CI: number;
  UTF8_ICELANDIC_CI: number;
  UTF8_LATVIAN_CI: number;
  UTF8_ROMANIAN_CI: number;
  UTF8_SLOVENIAN_CI: number;
  UTF8_POLISH_CI: number;
  UTF8_ESTONIAN_CI: number;
  UTF8_SPANISH_CI: number;
  UTF8_SWEDISH_CI: number;
  UTF8_TURKISH_CI: number;
  UTF8_CZECH_CI: number;
  UTF8_DANISH_CI: number;
  UTF8_LITHUANIAN_CI: number;
  UTF8_SLOVAK_CI: number;
  UTF8_SPANISH2_CI: number;
  UTF8_ROMAN_CI: number;
  UTF8_PERSIAN_CI: number;
  UTF8_ESPERANTO_CI: number;
  UTF8_HUNGARIAN_CI: number;
  UTF8_SINHALA_CI: number;
  UTF8_GERMAN2_CI: number;
  UTF8_CROATIAN_CI: number;
  UTF8_UNICODE_520_CI: number;
  UTF8_VIETNAMESE_CI: number;
  UTF8_GENERAL_MYSQL500_CI: number;
  UTF8MB4_UNICODE_CI: number;
  UTF8MB4_ICELANDIC_CI: number;
  UTF8MB4_LATVIAN_CI: number;
  UTF8MB4_ROMANIAN_CI: number;
  UTF8MB4_SLOVENIAN_CI: number;
  UTF8MB4_POLISH_CI: number;
  UTF8MB4_ESTONIAN_CI: number;
  UTF8MB4_SPANISH_CI: number;
  UTF8MB4_SWEDISH_CI: number;
  UTF8MB4_TURKISH_CI: number;
  UTF8MB4_CZECH_CI: number;
  UTF8MB4_DANISH_CI: number;
  UTF8MB4_LITHUANIAN_CI: number;
  UTF8MB4_SLOVAK_CI: number;
  UTF8MB4_SPANISH2_CI: number;
  UTF8MB4_ROMAN_CI: number;
  UTF8MB4_PERSIAN_CI: number;
  UTF8MB4_ESPERANTO_CI: number;
  UTF8MB4_HUNGARIAN_CI: number;
  UTF8MB4_SINHALA_CI: number;
  UTF8MB4_GERMAN2_CI: number;
  UTF8MB4_CROATIAN_CI: number;
  UTF8MB4_UNICODE_520_CI: number;
  UTF8MB4_VIETNAMESE_CI: number;
  GB18030_CHINESE_CI: number;
  GB18030_BIN: number;
  GB18030_UNICODE_520_CI: number;
  /** @deprecated */
  UTF8_GENERAL50_CI: number;
  UTF8MB4_0900_AI_CI: number;
  UTF8MB4_DE_PB_0900_AI_CI: number;
  UTF8MB4_IS_0900_AI_CI: number;
  UTF8MB4_LV_0900_AI_CI: number;
  UTF8MB4_RO_0900_AI_CI: number;
  UTF8MB4_SL_0900_AI_CI: number;
  UTF8MB4_PL_0900_AI_CI: number;
  UTF8MB4_ET_0900_AI_CI: number;
  UTF8MB4_ES_0900_AI_CI: number;
  UTF8MB4_SV_0900_AI_CI: number;
  UTF8MB4_TR_0900_AI_CI: number;
  UTF8MB4_CS_0900_AI_CI: number;
  UTF8MB4_DA_0900_AI_CI: number;
  UTF8MB4_LT_0900_AI_CI: number;
  UTF8MB4_SK_0900_AI_CI: number;
  UTF8MB4_ES_TRAD_0900_AI_CI: number;
  UTF8MB4_LA_0900_AI_CI: number;
  UTF8MB4_EO_0900_AI_CI: number;
  UTF8MB4_HU_0900_AI_CI: number;
  UTF8MB4_HR_0900_AI_CI: number;
  UTF8MB4_VI_0900_AI_CI: number;
  UTF8MB4_0900_AS_CS: number;
  UTF8MB4_DE_PB_0900_AS_CS: number;
  UTF8MB4_IS_0900_AS_CS: number;
  UTF8MB4_LV_0900_AS_CS: number;
  UTF8MB4_RO_0900_AS_CS: number;
  UTF8MB4_SL_0900_AS_CS: number;
  UTF8MB4_PL_0900_AS_CS: number;
  UTF8MB4_ET_0900_AS_CS: number;
  UTF8MB4_ES_0900_AS_CS: number;
  UTF8MB4_SV_0900_AS_CS: number;
  UTF8MB4_TR_0900_AS_CS: number;
  UTF8MB4_CS_0900_AS_CS: number;
  UTF8MB4_DA_0900_AS_CS: number;
  UTF8MB4_LT_0900_AS_CS: number;
  UTF8MB4_SK_0900_AS_CS: number;
  UTF8MB4_ES_TRAD_0900_AS_CS: number;
  UTF8MB4_LA_0900_AS_CS: number;
  UTF8MB4_EO_0900_AS_CS: number;
  UTF8MB4_HU_0900_AS_CS: number;
  UTF8MB4_HR_0900_AS_CS: number;
  UTF8MB4_VI_0900_AS_CS: number;
  UTF8MB4_JA_0900_AS_CS: number;
  UTF8MB4_JA_0900_AS_CS_KS: number;
  UTF8MB4_0900_AS_CI: number;
  UTF8MB4_RU_0900_AI_CI: number;
  UTF8MB4_RU_0900_AS_CS: number;
  UTF8MB4_ZH_0900_AS_CS: number;
  UTF8MB4_0900_BIN: number;

  BIG5: number;
  DEC8: number;
  CP850: number;
  HP8: number;
  KOI8R: number;
  LATIN1: number;
  LATIN2: number;
  SWE7: number;
  ASCII: number;
  UJIS: number;
  SJIS: number;
  HEBREW: number;
  TIS620: number;
  EUCKR: number;
  KOI8U: number;
  GB2312: number;
  GREEK: number;
  CP1250: number;
  GBK: number;
  LATIN5: number;
  ARMSCII8: number;
  UTF8: number;
  UCS2: number;
  CP866: number;
  KEYBCS2: number;
  MACCE: number;
  MACROMAN: number;
  CP852: number;
  LATIN7: number;
  UTF8MB4: number;
  CP1251: number;
  UTF16: number;
  UTF16LE: number;
  CP1256: number;
  CP1257: number;
  UTF32: number;
  CP932: number;
  EUCJPMS: number;
  GB18030: number;
  GEOSTD8: number;
}

/**
 * Constant `Charsets`.
 *
 * Please note that `Charsets` can only be accessed from the `mysql` object and not imported directly.
 */
declare const Charsets: Charsets;

export { Charsets };
