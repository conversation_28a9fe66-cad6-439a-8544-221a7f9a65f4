"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ImageIcon, Search, Filter, Download, Eye, Share2 } from "lucide-react"
import { VendorSidebar } from "@/components/vendor-sidebar"

const eventGalleries = [
  {
    id: 1,
    eventName: "Ngounou Arlane & Otantik Nuna Wedding",
    date: "Dec 15, 2024",
    totalPhotos: 156,
    totalVideos: 12,
    status: "published",
    coverImage: "/placeholder.svg?height=200&width=300",
  },
  {
    id: 2,
    eventName: "Ange & Marie Traditional Wedding",
    date: "Nov 20, 2024",
    totalPhotos: 203,
    totalVideos: 8,
    status: "published",
    coverImage: "/placeholder.svg?height=200&width=300",
  },
  {
    id: 3,
    eventName: "Cameroon Tech Summit 2024",
    date: "Dec 20, 2024",
    totalPhotos: 89,
    totalVideos: 15,
    status: "draft",
    coverImage: "/placeholder.svg?height=200&width=300",
  },
]

export default function VendorGallery() {
  const [searchTerm, setSearchTerm] = useState("")

  const filteredGalleries = eventGalleries.filter((gallery) =>
    gallery.eventName.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="flex h-screen bg-gray-50">
      <VendorSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Photo Gallery</h1>
            <p className="text-gray-600">Manage your event photo galleries</p>
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search galleries..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredGalleries.map((gallery) => (
              <Card key={gallery.id} className="border-0 shadow-lg overflow-hidden">
                <div className="relative">
                  <img
                    src={gallery.coverImage || "/placeholder.svg"}
                    alt={gallery.eventName}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-4 right-4">
                    <Badge variant={gallery.status === "published" ? "default" : "secondary"}>{gallery.status}</Badge>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-lg mb-2">{gallery.eventName}</h3>
                  <p className="text-sm text-gray-600 mb-3">{gallery.date}</p>

                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <ImageIcon className="h-4 w-4" />
                        <span>{gallery.totalPhotos} photos</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span>{gallery.totalVideos} videos</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                    <Button variant="outline" size="sm">
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
