# First login to get token
$loginBody = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json" -UseBasicParsing
    $loginData = $loginResponse.Content | ConvertFrom-Json
    $token = $loginData.token
    
    Write-Host "Login successful! Testing profit API..."
    
    # Test GET profit data
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    $profitResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Profit API Response:"
    Write-Host $profitResponse.Content
    
    Write-Host "`n--- Testing Analytics ---"
    
    # Test analytics
    $analyticsResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/analytics/profit?timeRange=6months" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Analytics API Response:"
    Write-Host $analyticsResponse.Content
    
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status: $($_.Exception.Response.StatusCode)"
    }
}
