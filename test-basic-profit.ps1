$loginBody = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

$loginResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json" -UseBasicParsing
$token = ($loginResponse.Content | ConvertFrom-Json).token

$headers = @{
    "Authorization" = "Bearer $token"
}

Write-Host "Testing basic profit endpoint..."
try {
    $profitResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Status: $($profitResponse.StatusCode)"
    Write-Host "Response: $($profitResponse.Content)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Status: $($_.Exception.Response.StatusCode)"
}
