const mysql = require("mysql2/promise")
require("dotenv").config()

async function cleanupDuplicates() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: "otantik_ems",
  })

  try {
    console.log("Cleaning up duplicate profit tracking records...")
    
    // Delete duplicate records, keeping only the first one for each event
    await connection.execute(`
      DELETE pt1 FROM profit_tracking pt1
      INNER JOIN profit_tracking pt2 
      WHERE pt1.id > pt2.id AND pt1.event_id = pt2.event_id
    `)
    
    console.log("Duplicates cleaned up!")
    
    // Show remaining records
    const [remaining] = await connection.execute("SELECT * FROM profit_tracking ORDER BY event_id")
    console.log("Remaining records:", remaining)

    await connection.end()
  } catch (error) {
    console.error("Error:", error)
  }
}

cleanupDuplicates()
