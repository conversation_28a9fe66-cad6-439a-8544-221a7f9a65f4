const express = require("express")
const app = express()

// Test route registration
app.get("/api/profit/summary", (req, res) => {
  res.json({ message: "Summary route works!" })
})

app.get("/api/profit/:eventId", (req, res) => {
  res.json({ message: "Event route works!", eventId: req.params.eventId })
})

app.get("/api/profit", (req, res) => {
  res.json({ message: "Basic profit route works!" })
})

app.listen(3002, () => {
  console.log("Test server running on port 3002")
})
