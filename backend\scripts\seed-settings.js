const mysql = require("mysql2/promise")
require("dotenv").config()

async function seedSettings() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: "otantik_ems",
  })

  try {
    console.log("Seeding settings with default values...")

    // Default settings data
    const defaultSettings = [
      // General Settings
      { key: 'system_name', value: 'otantikEMS', type: 'string', category: 'general', description: 'System name displayed in the application' },
      { key: 'admin_email', value: '<EMAIL>', type: 'string', category: 'general', description: 'Primary admin email address' },
      { key: 'system_description', value: 'Event Management System for Cameroon - Managing events across all regions', type: 'string', category: 'general', description: 'System description' },
      { key: 'default_timezone', value: 'Africa/Douala', type: 'string', category: 'general', description: 'Default timezone for the system' },
      { key: 'default_currency', value: 'XAF', type: 'string', category: 'general', description: 'Default currency code' },
      
      // Security Settings
      { key: 'two_factor_auth', value: 'true', type: 'boolean', category: 'security', description: 'Enable two-factor authentication' },
      { key: 'strong_password_policy', value: 'true', type: 'boolean', category: 'security', description: 'Enforce strong password policy' },
      { key: 'session_timeout', value: '60', type: 'number', category: 'security', description: 'Session timeout in minutes' },
      { key: 'max_login_attempts', value: '5', type: 'number', category: 'security', description: 'Maximum login attempts before lockout' },
      
      // Notification Settings
      { key: 'email_notifications', value: 'true', type: 'boolean', category: 'notifications', description: 'Enable email notifications' },
      { key: 'sms_notifications', value: 'false', type: 'boolean', category: 'notifications', description: 'Enable SMS notifications' },
      { key: 'smtp_server', value: 'smtp.gmail.com', type: 'string', category: 'notifications', description: 'SMTP server for email notifications' },
      { key: 'smtp_port', value: '587', type: 'number', category: 'notifications', description: 'SMTP port number' },
      
      // Regional Settings
      { key: 'default_region', value: 'Centre Region', type: 'string', category: 'regional', description: 'Default region for events' },
      { key: 'default_language', value: 'en', type: 'string', category: 'regional', description: 'Default system language' },
      { key: 'multi_language_support', value: 'true', type: 'boolean', category: 'regional', description: 'Enable multi-language support' },
      
      // Database Settings
      { key: 'auto_backup', value: 'true', type: 'boolean', category: 'database', description: 'Enable automatic database backups' },
      { key: 'backup_time', value: '02:00', type: 'string', category: 'database', description: 'Daily backup time' },
      { key: 'retention_days', value: '30', type: 'number', category: 'database', description: 'Backup retention period in days' },
      
      // Profit Settings
      { key: 'default_profit_margin', value: '20', type: 'number', category: 'profit', description: 'Default expected profit margin percentage' },
      { key: 'currency_symbol', value: 'FCFA', type: 'string', category: 'profit', description: 'Currency symbol for display' },
      { key: 'tax_rate', value: '19.25', type: 'number', category: 'profit', description: 'Default tax rate percentage' },
      { key: 'enable_expense_tracking', value: 'true', type: 'boolean', category: 'profit', description: 'Enable detailed expense tracking' }
    ]

    // Insert settings (ignore duplicates)
    for (const setting of defaultSettings) {
      await connection.execute(`
        INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, category, description) 
        VALUES (?, ?, ?, ?, ?)
      `, [setting.key, setting.value, setting.type, setting.category, setting.description])
    }

    // Insert sample profit data for existing events
    const [events] = await connection.execute("SELECT id, budget FROM events")
    
    for (const event of events) {
      // Generate sample revenue and expenses
      const revenue = event.budget || 0
      const expenses = revenue * 0.75 // 75% of budget as expenses
      
      await connection.execute(`
        INSERT IGNORE INTO profit_tracking (event_id, revenue, expenses, notes) 
        VALUES (?, ?, ?, ?)
      `, [event.id, revenue, expenses, 'Sample profit data'])
    }

    await connection.end()
    console.log("✅ Settings seeded successfully!")

  } catch (error) {
    console.error("❌ Settings seeding failed:", error)
    process.exit(1)
  }
}

seedSettings()
