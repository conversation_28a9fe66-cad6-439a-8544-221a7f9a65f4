"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, UserCheck, Clock, Users, QrCode, CheckCircle } from "lucide-react"
import { PlannerSidebar } from "@/components/planner-sidebar"

const checkinData = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON><PERSON> Arlane",
    email: "<EMAIL>",
    table: "Table 5",
    checkedIn: true,
    checkinTime: "2:30 PM",
    qrCode: "WED-2024-001-NA",
  },
  {
    id: 2,
    name: "Otantik Nuna",
    email: "<EMAIL>",
    table: "Table 5",
    checkedIn: true,
    checkinTime: "2:45 PM",
    qrCode: "WED-2024-002-ON",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    table: "Table 8",
    checkedIn: false,
    checkinTime: null,
    qrCode: "WED-2024-003-AM",
  },
  {
    id: 4,
    name: "Tantoh Emmanuel",
    email: "<EMAIL>",
    table: "Table 12",
    checkedIn: false,
    checkinTime: null,
    qrCode: "WED-2024-004-TE",
  },
]

export default function PlannerCheckin() {
  const [searchTerm, setSearchTerm] = useState("")
  const [qrScanMode, setQrScanMode] = useState(false)

  const filteredGuests = checkinData.filter(
    (guest) =>
      guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.email.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const checkedInCount = checkinData.filter((g) => g.checkedIn).length
  const totalGuests = checkinData.length

  return (
    <div className="flex h-screen bg-gray-50">
      <PlannerSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Live Check-in</h1>
              <p className="text-gray-600">Monitor guest arrivals in real-time</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant={qrScanMode ? "default" : "outline"} onClick={() => setQrScanMode(!qrScanMode)}>
                <QrCode className="h-4 w-4 mr-2" />
                {qrScanMode ? "Exit QR Mode" : "QR Scanner"}
              </Button>
              <Button className="bg-gradient-to-r from-green-600 to-emerald-600">
                <UserCheck className="h-4 w-4 mr-2" />
                Manual Check-in
              </Button>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Check-in Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Guests</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">{totalGuests}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Checked In</p>
                    <p className="text-3xl font-bold text-green-600 mt-2">{checkedInCount}</p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-3xl font-bold text-amber-600 mt-2">{totalGuests - checkedInCount}</p>
                  </div>
                  <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center">
                    <Clock className="h-6 w-6 text-amber-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Attendance Rate</p>
                    <p className="text-3xl font-bold text-purple-600 mt-2">
                      {Math.round((checkedInCount / totalGuests) * 100)}%
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <UserCheck className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* QR Scanner Mode */}
          {qrScanMode && (
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <QrCode className="h-5 w-5 text-blue-600" />
                  QR Code Scanner
                </CardTitle>
                <CardDescription>Scan guest QR codes for instant check-in</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-white rounded-lg border-2 border-dashed border-blue-300 p-8 text-center">
                  <QrCode className="h-16 w-16 mx-auto mb-4 text-blue-400" />
                  <p className="text-lg font-medium text-gray-700 mb-2">Ready to Scan</p>
                  <p className="text-gray-500">Position the QR code within the camera frame</p>
                  <div className="mt-4 h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <div className="border-2 border-blue-500 w-48 h-48 rounded-lg flex items-center justify-center">
                      <p className="text-blue-600 font-medium">Camera View</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Guest List */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <UserCheck className="h-5 w-5 text-green-600" />
                    Guest Check-in Status
                  </CardTitle>
                  <CardDescription>Monitor and manage guest arrivals</CardDescription>
                </div>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search guests..."
                    className="pl-10 w-80"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredGuests.map((guest) => (
                  <div
                    key={guest.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center gap-4">
                      <Avatar>
                        <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
                        <AvatarFallback>
                          {guest.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{guest.name}</h3>
                        <p className="text-sm text-gray-600">{guest.email}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline">{guest.table}</Badge>
                          <Badge variant="outline" className="text-xs">
                            {guest.qrCode}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      {guest.checkedIn ? (
                        <div className="text-right">
                          <Badge className="bg-green-100 text-green-800 mb-1">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Checked In
                          </Badge>
                          <p className="text-xs text-gray-500">at {guest.checkinTime}</p>
                        </div>
                      ) : (
                        <div className="text-right">
                          <Badge variant="secondary" className="mb-1">
                            <Clock className="h-3 w-3 mr-1" />
                            Pending
                          </Badge>
                          <Button size="sm" className="bg-green-600 hover:bg-green-700">
                            Check In
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
