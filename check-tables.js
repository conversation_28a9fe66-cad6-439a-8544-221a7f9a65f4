const mysql = require("mysql2/promise")
require("dotenv").config()

async function checkTables() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: "otantik_ems",
  })

  try {
    console.log("Checking database tables...")
    
    // Show all tables
    const [tables] = await connection.execute("SHOW TABLES")
    console.log("Available tables:", tables.map(t => Object.values(t)[0]))
    
    // Check if event_expenses table exists
    const [expensesCheck] = await connection.execute("SHOW TABLES LIKE 'event_expenses'")
    if (expensesCheck.length > 0) {
      console.log("\nEvent expenses table exists")
      const [expenses] = await connection.execute("SELECT * FROM event_expenses LIMIT 5")
      console.log("Sample expenses:", expenses)
    } else {
      console.log("\nEvent expenses table does NOT exist")
    }
    
    // Check profit_tracking table
    const [profitCheck] = await connection.execute("SHOW TABLES LIKE 'profit_tracking'")
    if (profitCheck.length > 0) {
      console.log("\nProfit tracking table exists")
      const [profits] = await connection.execute("SELECT * FROM profit_tracking LIMIT 5")
      console.log("Sample profits:", profits)
    } else {
      console.log("\nProfit tracking table does NOT exist")
    }

    await connection.end()
  } catch (error) {
    console.error("Error:", error)
  }
}

checkTables()
