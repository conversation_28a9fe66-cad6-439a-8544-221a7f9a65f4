import apiClient from "@/lib/api"

export interface ProfitData {
  id: number
  event_id: number
  revenue: string
  expenses: string
  profit: string
  profit_margin: string
  notes: string
  created_at: string
  updated_at: string
  event_title?: string
  event_date?: string
  venue?: string
  first_name?: string
  last_name?: string
}

export interface EventExpense {
  id: number
  event_id: number
  expense_category: string
  description: string
  amount: string
  expense_date: string
  vendor_id?: number
  receipt_url?: string
  status: 'pending' | 'approved' | 'paid'
  created_by: number
  created_at: string
  updated_at: string
  vendor_name?: string
  first_name?: string
  last_name?: string
}

export interface EventProfitDetails {
  event: {
    id: number
    title: string
    budget: string
    event_date: string
    venue: string
  }
  profit: ProfitData | null
  expenses: EventExpense[]
}

export interface ProfitAnalytics {
  month: string
  total_events: number
  total_revenue: string
  total_expenses: string
  total_profit: string
  avg_profit_margin: string
}

export interface CreateExpenseData {
  expense_category: string
  description: string
  amount: number
  expense_date: string
  vendor_id?: number
  receipt_url?: string
}

export interface UpdateExpenseData extends CreateExpenseData {
  status?: 'pending' | 'approved' | 'paid'
}

class ProfitService {
  async getAllProfitData(): Promise<ProfitData[]> {
    return apiClient.request("/profit")
  }

  async getEventProfit(eventId: number): Promise<EventProfitDetails> {
    return apiClient.request(`/profit/${eventId}`)
  }

  async updateEventProfit(eventId: number, data: {
    revenue?: number
    expenses?: number
    notes?: string
  }): Promise<ProfitData> {
    return apiClient.request(`/profit/${eventId}`, {
      method: "PUT",
      body: JSON.stringify(data),
    })
  }

  async addExpense(eventId: number, expense: CreateExpenseData): Promise<{ id: number; message: string }> {
    return apiClient.request(`/events/${eventId}/expenses`, {
      method: "POST",
      body: JSON.stringify(expense),
    })
  }

  async updateExpense(expenseId: number, expense: UpdateExpenseData): Promise<{ message: string }> {
    return apiClient.request(`/expenses/${expenseId}`, {
      method: "PUT",
      body: JSON.stringify(expense),
    })
  }

  async deleteExpense(expenseId: number): Promise<{ message: string }> {
    return apiClient.request(`/expenses/${expenseId}`, {
      method: "DELETE",
    })
  }

  async getProfitAnalytics(timeRange: '1month' | '3months' | '6months' | '1year' = '6months'): Promise<ProfitAnalytics[]> {
    return apiClient.request(`/analytics/profit?timeRange=${timeRange}`)
  }

  // Helper methods for calculations
  calculateProfitMargin(revenue: number, expenses: number): number {
    if (revenue <= 0) return 0
    return ((revenue - expenses) / revenue) * 100
  }

  formatCurrency(amount: string | number, currency: string = 'FCFA'): string {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
    return `${numAmount.toLocaleString()} ${currency}`
  }

  getProfitStatus(profitMargin: number): 'excellent' | 'good' | 'average' | 'poor' {
    if (profitMargin >= 30) return 'excellent'
    if (profitMargin >= 20) return 'good'
    if (profitMargin >= 10) return 'average'
    return 'poor'
  }

  getProfitStatusColor(status: string): string {
    switch (status) {
      case 'excellent': return 'text-green-600'
      case 'good': return 'text-blue-600'
      case 'average': return 'text-yellow-600'
      case 'poor': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }
}

export const profitService = new ProfitService()
