# Test the real profit functionality
$loginBody = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-<PERSON>son

try {
    Write-Host "=== Testing Real Profit Management ==="
    
    # Login
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json" -UseBasicParsing
    $loginData = $loginResponse.Content | ConvertFrom-Json
    $token = $loginData.token
    
    Write-Host "✅ Login successful!"
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # Test 1: Get all profit data (should show real events with calculated profit)
    Write-Host "`n--- Test 1: Get All Profit Data (Real Events) ---"
    $profitResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Status: $($profitResponse.StatusCode)"
    $profitData = $profitResponse.Content | ConvertFrom-Json
    Write-Host "Found $($profitData.Count) events"
    foreach ($event in $profitData) {
        Write-Host "- $($event.event_title): Revenue=$($event.revenue), Expenses=$($event.expenses), Profit=$($event.profit)"
    }
    
    # Test 2: Get specific event details
    Write-Host "`n--- Test 2: Get Event 1 Details ---"
    $eventResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit/1" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Status: $($eventResponse.StatusCode)"
    Write-Host "Response: $($eventResponse.Content)"
    
    # Test 3: Update profit data for event 1
    Write-Host "`n--- Test 3: Update Profit Data for Event 1 ---"
    $updateBody = @{
        revenue = 20000000
        expenses = 12000000
        notes = "Updated profit data - real numbers"
    } | ConvertTo-Json
    
    $updateResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit/1" -Method PUT -Body $updateBody -Headers $headers -UseBasicParsing
    Write-Host "Status: $($updateResponse.StatusCode)"
    Write-Host "Response: $($updateResponse.Content)"
    
    # Test 4: Verify the update
    Write-Host "`n--- Test 4: Verify Update ---"
    $verifyResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit/1" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Status: $($verifyResponse.StatusCode)"
    Write-Host "Updated Data: $($verifyResponse.Content)"
    
    Write-Host "`n✅ All tests completed successfully!"
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status: $($_.Exception.Response.StatusCode)"
    }
}
