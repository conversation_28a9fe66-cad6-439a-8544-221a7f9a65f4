"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { User, Bell, Shield, Palette, Calendar, DollarSign, TrendingUp, Loader2, Plus, Edit, Trash2 } from "lucide-react"
import { PlannerSidebar } from "@/components/planner-sidebar"
import apiClient from "@/lib/api"
import { toast } from "sonner"

export default function PlannerSettings() {
  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    push: true,
    guestCheckin: true,
    vendorUpdates: true,
  })

  const [eventSettings, setEventSettings] = useState({
    autoCheckin: false,
    qrExpiry: "",
    guestLimit: "",
    allowPlusOnes: true,
  })

  const [profitData, setProfitData] = useState([])
  const [loadingProfit, setLoadingProfit] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [showProfitForm, setShowProfitForm] = useState(false)
  const [profitForm, setProfitForm] = useState({
    revenue: "",
    expenses: "",
    notes: ""
  })

  useEffect(() => {
    loadProfitData()
  }, [])

  const loadProfitData = async () => {
    try {
      setLoadingProfit(true)
      const data = await apiClient.getProfitData()
      setProfitData(data)
    } catch (error) {
      console.error("Failed to load profit data:", error)
      toast.error("Failed to load profit data")
    } finally {
      setLoadingProfit(false)
    }
  }

  const handleUpdateProfit = async (eventId) => {
    try {
      const revenue = parseFloat(profitForm.revenue) || 0
      const expenses = parseFloat(profitForm.expenses) || 0

      await apiClient.updateEventProfit(eventId, {
        revenue,
        expenses,
        notes: profitForm.notes
      })

      toast.success("Profit data updated successfully!")
      setShowProfitForm(false)
      setProfitForm({ revenue: "", expenses: "", notes: "" })
      loadProfitData()
    } catch (error) {
      console.error("Failed to update profit:", error)
      toast.error("Failed to update profit data")
    }
  }

  const calculateTotals = () => {
    return profitData.reduce(
      (acc, item) => ({
        revenue: acc.revenue + parseFloat(item.revenue),
        expenses: acc.expenses + parseFloat(item.expenses),
        profit: acc.profit + parseFloat(item.profit),
      }),
      { revenue: 0, expenses: 0, profit: 0 }
    )
  }

  const getProfitStatusBadge = (margin) => {
    const marginNum = parseFloat(margin)
    if (marginNum >= 30) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>
    if (marginNum >= 20) return <Badge className="bg-blue-100 text-blue-800">Good</Badge>
    if (marginNum >= 10) return <Badge className="bg-yellow-100 text-yellow-800">Average</Badge>
    return <Badge className="bg-red-100 text-red-800">Poor</Badge>
  }

  const formatCurrency = (amount) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
    return `${numAmount.toLocaleString()} FCFA`
  }

  const totals = profitData.reduce(
    (acc, item) => ({
      revenue: acc.revenue + parseFloat(item.revenue || 0),
      expenses: acc.expenses + parseFloat(item.expenses || 0),
      profit: acc.profit + parseFloat(item.profit || 0),
    }),
    { revenue: 0, expenses: 0, profit: 0 }
  )

  return (
    <div className="flex h-screen bg-gray-50">
      <PlannerSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600">Manage your account and event preferences</p>
          </div>
        </div>

        <div className="p-6">
          <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Notifications
              </TabsTrigger>
              <TabsTrigger value="events" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Events
              </TabsTrigger>
              <TabsTrigger value="profit" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Profit
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Security
              </TabsTrigger>
              <TabsTrigger value="appearance" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Appearance
              </TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5 text-blue-600" />
                    Profile Information
                  </CardTitle>
                  <CardDescription>Update your personal information and contact details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name</Label>
                      <Input id="firstName" defaultValue="John" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input id="lastName" defaultValue="Doe" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input id="email" type="email" defaultValue="<EMAIL>" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" type="tel" defaultValue="+****************" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company">Company</Label>
                    <Input id="company" defaultValue="Event Planning Co." />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea id="bio" placeholder="Tell us about yourself..." rows={4} />
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Save Changes</Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5 text-blue-600" />
                    Notification Preferences
                  </CardTitle>
                  <CardDescription>Choose how you want to be notified about events and updates</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="email-notifications">Email Notifications</Label>
                        <p className="text-sm text-gray-500">Receive notifications via email</p>
                      </div>
                      <Switch
                        id="email-notifications"
                        checked={notifications.email}
                        onCheckedChange={(checked) => setNotifications({ ...notifications, email: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="sms-notifications">SMS Notifications</Label>
                        <p className="text-sm text-gray-500">Receive notifications via text message</p>
                      </div>
                      <Switch
                        id="sms-notifications"
                        checked={notifications.sms}
                        onCheckedChange={(checked) => setNotifications({ ...notifications, sms: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="push-notifications">Push Notifications</Label>
                        <p className="text-sm text-gray-500">Receive push notifications in your browser</p>
                      </div>
                      <Switch
                        id="push-notifications"
                        checked={notifications.push}
                        onCheckedChange={(checked) => setNotifications({ ...notifications, push: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="guest-checkin">Guest Check-in Alerts</Label>
                        <p className="text-sm text-gray-500">Get notified when guests check in</p>
                      </div>
                      <Switch
                        id="guest-checkin"
                        checked={notifications.guestCheckin}
                        onCheckedChange={(checked) => setNotifications({ ...notifications, guestCheckin: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="vendor-updates">Vendor Updates</Label>
                        <p className="text-sm text-gray-500">Receive updates from event vendors</p>
                      </div>
                      <Switch
                        id="vendor-updates"
                        checked={notifications.vendorUpdates}
                        onCheckedChange={(checked) => setNotifications({ ...notifications, vendorUpdates: checked })}
                      />
                    </div>
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Save Preferences</Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="events" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-blue-600" />
                    Event Settings
                  </CardTitle>
                  <CardDescription>Configure default settings for your events</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-checkin">Auto Check-in</Label>
                        <p className="text-sm text-gray-500">Automatically check in guests when they scan QR codes</p>
                      </div>
                      <Switch
                        id="auto-checkin"
                        checked={eventSettings.autoCheckin}
                        onCheckedChange={(checked) => setEventSettings({ ...eventSettings, autoCheckin: checked })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="qr-expiry">QR Code Expiry (hours)</Label>
                      <Select
                        value={eventSettings.qrExpiry}
                        onValueChange={(value) => setEventSettings({ ...eventSettings, qrExpiry: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 hour</SelectItem>
                          <SelectItem value="6">6 hours</SelectItem>
                          <SelectItem value="12">12 hours</SelectItem>
                          <SelectItem value="24">24 hours</SelectItem>
                          <SelectItem value="48">48 hours</SelectItem>
                          <SelectItem value="never">Never expire</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="guest-limit">Default Guest Limit</Label>
                      <Input
                        id="guest-limit"
                        type="number"
                        value={eventSettings.guestLimit}
                        onChange={(e) => setEventSettings({ ...eventSettings, guestLimit: e.target.value })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="plus-ones">Allow Plus Ones</Label>
                        <p className="text-sm text-gray-500">Allow guests to bring additional attendees</p>
                      </div>
                      <Switch
                        id="plus-ones"
                        checked={eventSettings.allowPlusOnes}
                        onCheckedChange={(checked) => setEventSettings({ ...eventSettings, allowPlusOnes: checked })}
                      />
                    </div>
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Save Settings</Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="profit" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    Profit Management
                  </CardTitle>
                  <CardDescription>Track revenue, expenses, and profitability for your events</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {loadingProfit ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin" />
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {/* Summary Cards */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card>
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                                <p className="text-2xl font-bold text-green-600">
                                  {formatCurrency(totals.revenue)}
                                </p>
                              </div>
                              <TrendingUp className="h-8 w-8 text-green-600" />
                            </div>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm font-medium text-gray-600">Total Expenses</p>
                                <p className="text-2xl font-bold text-red-600">
                                  {formatCurrency(totals.expenses)}
                                </p>
                              </div>
                              <TrendingUp className="h-8 w-8 text-red-600" />
                            </div>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-sm font-medium text-gray-600">Net Profit</p>
                                <p className="text-2xl font-bold text-blue-600">
                                  {formatCurrency(totals.profit)}
                                </p>
                              </div>
                              <DollarSign className="h-8 w-8 text-blue-600" />
                            </div>
                          </CardContent>
                        </Card>
                      </div>

                      {/* Events List */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Event Profitability</h3>
                        {profitData.length === 0 ? (
                          <p className="text-gray-500 text-center py-8">No profit data available</p>
                        ) : (
                          <div className="space-y-3">
                            {profitData.map((event) => (
                              <Card key={event.event_id} className="p-4">
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <h4 className="font-medium">{event.event_title}</h4>
                                    <p className="text-sm text-gray-500">{event.venue}</p>
                                  </div>
                                  <div className="flex items-center gap-4">
                                    <div className="text-right">
                                      <p className="text-sm text-gray-600">Revenue: {formatCurrency(event.revenue)}</p>
                                      <p className="text-sm text-gray-600">Profit: {formatCurrency(event.profit)}</p>
                                    </div>
                                    {getProfitStatusBadge(event.profit_margin)}
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        setSelectedEvent(event)
                                        setProfitForm({
                                          revenue: event.revenue,
                                          expenses: event.expenses,
                                          notes: event.notes || ""
                                        })
                                        setShowProfitForm(true)
                                      }}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              </Card>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Edit Form Modal */}
                      {showProfitForm && selectedEvent && (
                        <Card className="border-2 border-blue-200">
                          <CardHeader>
                            <CardTitle>Edit Profit Data - {selectedEvent.event_title}</CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="revenue">Revenue (FCFA)</Label>
                                <Input
                                  id="revenue"
                                  type="number"
                                  value={profitForm.revenue}
                                  onChange={(e) => setProfitForm({ ...profitForm, revenue: e.target.value })}
                                />
                              </div>
                              <div>
                                <Label htmlFor="expenses">Expenses (FCFA)</Label>
                                <Input
                                  id="expenses"
                                  type="number"
                                  value={profitForm.expenses}
                                  onChange={(e) => setProfitForm({ ...profitForm, expenses: e.target.value })}
                                />
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="notes">Notes</Label>
                              <Textarea
                                id="notes"
                                value={profitForm.notes}
                                onChange={(e) => setProfitForm({ ...profitForm, notes: e.target.value })}
                                placeholder="Add notes about this profit data..."
                              />
                            </div>
                            <div className="flex gap-2">
                              <Button
                                onClick={() => handleUpdateProfit(selectedEvent.event_id)}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                Save Changes
                              </Button>
                              <Button
                                variant="outline"
                                onClick={() => {
                                  setShowProfitForm(false)
                                  setSelectedEvent(null)
                                }}
                              >
                                Cancel
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-blue-600" />
                    Security Settings
                  </CardTitle>
                  <CardDescription>Manage your account security and privacy</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="current-password">Current Password</Label>
                      <Input id="current-password" type="password" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="new-password">New Password</Label>
                      <Input id="new-password" type="password" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="confirm-password">Confirm New Password</Label>
                      <Input id="confirm-password" type="password" />
                    </div>
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Update Password</Button>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Two-Factor Authentication</h3>
                    <p className="text-gray-600 mb-4">Add an extra layer of security to your account</p>
                    <Button variant="outline">Enable 2FA</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="appearance" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5 text-blue-600" />
                    Appearance Settings
                  </CardTitle>
                  <CardDescription>Customize the look and feel of your dashboard</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Theme</Label>
                      <Select defaultValue="light">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                          <SelectItem value="system">System</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Language</Label>
                      <Select defaultValue="en">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="fr">French</SelectItem>
                          <SelectItem value="es">Spanish</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Timezone</Label>
                      <Select defaultValue="utc">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="utc">UTC</SelectItem>
                          <SelectItem value="est">Eastern Time</SelectItem>
                          <SelectItem value="pst">Pacific Time</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Save Preferences</Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
