"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { QrCode, Calendar, MapPin, Users, Download, ImageIcon, Heart, Share2, Clock, CheckCircle } from "lucide-react"

const guestData = {
  name: "<PERSON><PERSON><PERSON><PERSON>",
  email: "<EMAIL>",
  invitationCode: "WED-2024-001",
  event: {
    name: "Otantik Nuna & Bimm Audrey Wedding",
    date: "December 15, 2024",
    time: "6:00 PM",
    venue: "Hilton Hotel Yaoundé",
    address: "Boulevard du 20 Mai, Yaoundé, Centre Region",
    dresscode: "Formal/Traditional",
    table: "Table 5",
    seat: "Seat 3",
  },
  rsvpStatus: "confirmed",
  checkedIn: false,
  plusOne: true,
}

const eventPhotos = [
  { id: 1, url: "/placeholder.svg?height=200&width=300", caption: "Ceremony moments" },
  { id: 2, url: "/placeholder.svg?height=200&width=300", caption: "Traditional dance" },
  { id: 3, url: "/placeholder.svg?height=200&width=300", caption: "Family photos" },
  { id: 4, url: "/placeholder.svg?height=200&width=300", caption: "Reception dinner" },
  { id: 5, url: "/placeholder.svg?height=200&width=300", caption: "Cake cutting" },
  { id: 6, url: "/placeholder.svg?height=200&width=300", caption: "Group celebration" },
]

const Eye = ImageIcon // Declare Eye variable

export default function GuestPortal() {
  const [activeTab, setActiveTab] = useState("invitation")

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Welcome, {guestData.name}!</h1>
            <p className="text-gray-600">Your personal event portal</p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="px-3 py-1">
              <QrCode className="h-4 w-4 mr-1" />
              {guestData.invitationCode}
            </Badge>
            {guestData.rsvpStatus === "confirmed" && (
              <Badge className="bg-green-100 text-green-800">
                <CheckCircle className="h-4 w-4 mr-1" />
                RSVP Confirmed
              </Badge>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="invitation">Invitation</TabsTrigger>
            <TabsTrigger value="event-info">Event Info</TabsTrigger>
            <TabsTrigger value="checkin">Check-in</TabsTrigger>
            <TabsTrigger value="photos">Event Photos</TabsTrigger>
          </TabsList>

          <TabsContent value="invitation" className="space-y-6">
            {/* Digital Invitation */}
            <Card className="border-0 shadow-2xl bg-gradient-to-br from-amber-500 to-yellow-500 text-white">
              <CardContent className="p-8 text-center">
                <div className="mb-6">
                  <Heart className="h-12 w-12 mx-auto mb-4 opacity-80" />
                  <h2 className="text-3xl font-bold mb-2">You're Invited!</h2>
                </div>

                <div className="space-y-4 text-lg opacity-90">
                  <h3 className="text-2xl font-semibold">{guestData.event.name}</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-center gap-2">
                      <Calendar className="h-5 w-5" />
                      <span>{guestData.event.date}</span>
                    </div>
                    <div className="flex items-center justify-center gap-2">
                      <Clock className="h-5 w-5" />
                      <span>{guestData.event.time}</span>
                    </div>
                    <div className="flex items-center justify-center gap-2">
                      <MapPin className="h-5 w-5" />
                      <span>{guestData.event.venue}</span>
                    </div>
                  </div>
                </div>

                <div className="mt-8 p-6 bg-white/20 rounded-lg">
                  <p className="text-sm leading-relaxed">
                    Join us as we celebrate this special day with family and friends. Your presence would make our joy
                    complete. Come and be part of our beautiful Cameroon wedding celebration!
                  </p>
                </div>

                <div className="mt-8">
                  <div className="bg-white p-4 rounded-lg inline-block">
                    <QrCode className="h-24 w-24 text-gray-800 mx-auto" />
                  </div>
                  <p className="text-sm mt-3 opacity-80">Scan this QR code at the venue to check in</p>
                </div>

                <div className="mt-6 flex items-center justify-center gap-4">
                  <Button variant="outline" className="bg-white/20 border-white/30 text-white hover:bg-white/30">
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF
                  </Button>
                  <Button variant="outline" className="bg-white/20 border-white/30 text-white hover:bg-white/30">
                    <Share2 className="h-4 w-4 mr-2" />
                    Share
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="event-info" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle>Event Details</CardTitle>
                  <CardDescription>Everything you need to know about the event</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5 text-amber-600" />
                    <div>
                      <p className="font-medium">{guestData.event.date}</p>
                      <p className="text-sm text-gray-600">{guestData.event.time}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-amber-600 mt-1" />
                    <div>
                      <p className="font-medium">{guestData.event.venue}</p>
                      <p className="text-sm text-gray-600">{guestData.event.address}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Users className="h-5 w-5 text-amber-600" />
                    <div>
                      <p className="font-medium">Dress Code</p>
                      <p className="text-sm text-gray-600">{guestData.event.dresscode}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle>Your Seating Information</CardTitle>
                  <CardDescription>Your assigned table and seat</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center p-6 bg-amber-50 rounded-lg">
                    <div className="text-3xl font-bold text-amber-600 mb-2">{guestData.event.table}</div>
                    <div className="text-lg font-medium text-gray-900">{guestData.event.seat}</div>
                    {guestData.plusOne && (
                      <Badge variant="outline" className="mt-2">
                        Plus One Included
                      </Badge>
                    )}
                  </div>

                  <div className="text-center">
                    <Button variant="outline" className="w-full bg-transparent">
                      <MapPin className="h-4 w-4 mr-2" />
                      View Seating Chart
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Directions */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle>Getting There</CardTitle>
                <CardDescription>Directions and transportation information</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Venue Address:</h4>
                  <p className="text-gray-700 mb-4">{guestData.event.address}</p>

                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <MapPin className="h-4 w-4 mr-1" />
                      Open in Maps
                    </Button>
                    <Button variant="outline" size="sm">
                      Get Directions
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="checkin" className="space-y-6">
            <Card className="border-0 shadow-lg">
              <CardHeader className="text-center">
                <CardTitle>Event Check-in</CardTitle>
                <CardDescription>Use your QR code to check in at the venue</CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-6">
                {!guestData.checkedIn ? (
                  <>
                    <div className="bg-amber-50 p-8 rounded-lg">
                      <QrCode className="h-32 w-32 mx-auto mb-4 text-amber-600" />
                      <h3 className="text-xl font-semibold mb-2">Ready to Check In</h3>
                      <p className="text-gray-600 mb-4">Show this QR code to the event staff at the entrance</p>
                      <Badge variant="outline" className="text-lg px-4 py-2">
                        {guestData.invitationCode}
                      </Badge>
                    </div>

                    <div className="text-sm text-gray-600">
                      <p>• Arrive 30 minutes before the event starts</p>
                      <p>• Have your QR code ready on your phone</p>
                      <p>• Bring a valid ID for verification</p>
                    </div>
                  </>
                ) : (
                  <div className="bg-green-50 p-8 rounded-lg">
                    <CheckCircle className="h-32 w-32 mx-auto mb-4 text-green-600" />
                    <h3 className="text-xl font-semibold mb-2 text-green-800">Successfully Checked In!</h3>
                    <p className="text-green-700">Welcome to the event. Enjoy the celebration!</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="photos" className="space-y-6">
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Event Photo Gallery</CardTitle>
                    <CardDescription>Beautiful moments captured during the event</CardDescription>
                  </div>
                  <Button variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download All
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {eventPhotos.map((photo) => (
                    <div key={photo.id} className="group relative overflow-hidden rounded-lg">
                      <img
                        src={photo.url || "/placeholder.svg"}
                        alt={photo.caption}
                        className="w-full h-48 object-cover transition-transform group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline" className="bg-white/20 border-white/30 text-white">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" className="bg-white/20 border-white/30 text-white">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
                        <p className="text-white text-sm">{photo.caption}</p>
                      </div>
                    </div>
                  ))}
                </div>

                {eventPhotos.length === 0 && (
                  <div className="text-center py-12">
                    <ImageIcon className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Photos Coming Soon</h3>
                    <p className="text-gray-600">Event photos will be available here after the celebration</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Thank You Message */}
            <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-yellow-50">
              <CardContent className="p-8 text-center">
                <Heart className="h-12 w-12 mx-auto mb-4 text-amber-600" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Thank You for Celebrating With Us!</h3>
                <p className="text-gray-700 leading-relaxed">
                  Your presence made our special day even more memorable. We hope you enjoyed the celebration as much as
                  we did. These photos capture the beautiful moments we shared together.
                </p>
                <div className="mt-6">
                  <Button className="bg-gradient-to-r from-amber-500 to-yellow-500">
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Your Experience
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
