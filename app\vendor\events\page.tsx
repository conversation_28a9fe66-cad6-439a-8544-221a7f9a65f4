"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Users, Camera, Upload } from "lucide-react"
import { VendorSidebar } from "@/components/vendor-sidebar"

const assignedEvents = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON><PERSON> Arlane & Otantik Nuna Wedding",
    date: "Dec 15, 2024",
    venue: "Hilton Hotel Yaoundé",
    status: "upcoming",
    role: "Photographer",
    planner: "Bimm Audrey",
    guests: 250,
    mediaUploaded: 0,
    totalMedia: 0,
  },
  {
    id: 2,
    name: "<PERSON><PERSON> <PERSON> Traditional Wedding",
    date: "Nov 20, 2024",
    venue: "Cultural Center Douala",
    status: "completed",
    role: "Photographer",
    planner: "Bimm Audrey",
    guests: 400,
    mediaUploaded: 156,
    totalMedia: 200,
  },
  {
    id: 3,
    name: "Cameroon Tech Summit 2024",
    date: "Dec 20, 2024",
    venue: "Palais des Congrès Yaoundé",
    status: "upcoming",
    role: "Videographer",
    planner: "<PERSON><PERSON><PERSON> Emmanuel",
    guests: 800,
    mediaUploaded: 0,
    totalMedia: 0,
  },
]

export default function VendorEvents() {
  return (
    <div className="flex h-screen bg-gray-50">
      <VendorSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Events</h1>
            <p className="text-gray-600">Events where you're assigned as vendor</p>
          </div>
        </div>

        <div className="p-6">
          <div className="space-y-6">
            {assignedEvents.map((event) => (
              <Card key={event.id} className="border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold">{event.name}</h3>
                      <p className="text-gray-600">{event.venue}</p>
                    </div>
                    <Badge variant={event.status === "completed" ? "default" : "secondary"}>{event.status}</Badge>
                  </div>

                  <div className="grid md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-600" />
                      <span className="text-sm">{event.date}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-gray-600" />
                      <span className="text-sm">{event.guests} guests</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Camera className="h-4 w-4 text-gray-600" />
                      <span className="text-sm">{event.role}</span>
                    </div>
                  </div>

                  {event.status === "completed" && (
                    <div className="mb-4 p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span className="text-gray-600">Media Upload Progress</span>
                        <span className="font-medium">
                          {event.mediaUploaded}/{event.totalMedia} photos
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${(event.mediaUploaded / event.totalMedia) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      Planner: <span className="font-medium">{event.planner}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {event.status === "completed" && (
                        <Button variant="outline" size="sm">
                          <Upload className="h-4 w-4 mr-1" />
                          Upload Media
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
