const mysql = require("mysql2/promise")
require("dotenv").config()

async function runSimpleMigration() {
  // First connection without database to create the database
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
  })

  try {
    console.log("Creating database...")
    await connection.execute("CREATE DATABASE IF NOT EXISTS otantik_ems")
    console.log("✅ Database created successfully!")
    
    await connection.end()

    // Second connection to the specific database
    const dbConnection = await mysql.createConnection({
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "root",
      password: process.env.DB_PASSWORD || "",
      database: "otantik_ems",
    })

    console.log("Creating tables...")

    // Users table
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'planner', 'vendor', 'guest') NOT NULL,
        region VARCHAR(100),
        phone VARCHAR(20),
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)
    console.log("✅ Users table created")

    // Events table
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS events (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        event_date DATE NOT NULL,
        event_time TIME NOT NULL,
        venue VARCHAR(255) NOT NULL,
        address TEXT,
        region VARCHAR(100),
        expected_guests INT DEFAULT 0,
        budget DECIMAL(15,2),
        planner_id INT,
        status ENUM('planning', 'confirmed', 'completed', 'cancelled') DEFAULT 'planning',
        invitation_template VARCHAR(50) DEFAULT 'traditional',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (planner_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `)
    console.log("✅ Events table created")

    // Guests table
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS guests (
        id INT PRIMARY KEY AUTO_INCREMENT,
        event_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(20),
        table_number VARCHAR(10),
        seat_number VARCHAR(10),
        dietary_requirements TEXT,
        plus_one BOOLEAN DEFAULT FALSE,
        rsvp_status ENUM('pending', 'confirmed', 'declined') DEFAULT 'pending',
        checked_in BOOLEAN DEFAULT FALSE,
        check_in_time TIMESTAMP NULL,
        whatsapp_sent BOOLEAN DEFAULT FALSE,
        whatsapp_sent_at TIMESTAMP NULL,
        qr_code VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
      )
    `)
    console.log("✅ Guests table created")

    // Vendors table
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS vendors (
        id INT PRIMARY KEY AUTO_INCREMENT,
        business_name VARCHAR(255) NOT NULL,
        contact_person VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(20) NOT NULL,
        service_type ENUM('photographer', 'videographer', 'caterer', 'decorator', 'musician', 'dj', 'florist', 'transportation', 'other') NOT NULL,
        region VARCHAR(100),
        address TEXT,
        description TEXT,
        website VARCHAR(255),
        rating DECIMAL(3,2) DEFAULT 0.00,
        total_events INT DEFAULT 0,
        status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)
    console.log("✅ Vendors table created")

    // Settings table
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        category VARCHAR(50) DEFAULT 'general',
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)
    console.log("✅ Settings table created")

    // Profit tracking table
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS profit_tracking (
        id INT PRIMARY KEY AUTO_INCREMENT,
        event_id INT NOT NULL,
        revenue DECIMAL(15,2) DEFAULT 0.00,
        expenses DECIMAL(15,2) DEFAULT 0.00,
        profit DECIMAL(15,2) GENERATED ALWAYS AS (revenue - expenses) STORED,
        profit_margin DECIMAL(5,2) GENERATED ALWAYS AS (
          CASE
            WHEN revenue > 0 THEN ((revenue - expenses) / revenue) * 100
            ELSE 0
          END
        ) STORED,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
      )
    `)
    console.log("✅ Profit tracking table created")

    // Event expenses table
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS event_expenses (
        id INT PRIMARY KEY AUTO_INCREMENT,
        event_id INT NOT NULL,
        expense_category VARCHAR(100) NOT NULL,
        description TEXT,
        amount DECIMAL(15,2) NOT NULL,
        expense_date DATE NOT NULL,
        vendor_id INT,
        receipt_url VARCHAR(500),
        status ENUM('pending', 'approved', 'paid') DEFAULT 'pending',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
        FOREIGN KEY (vendor_id) REFERENCES vendors(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
      )
    `)
    console.log("✅ Event expenses table created")

    await dbConnection.end()
    console.log("✅ All tables created successfully!")

  } catch (error) {
    console.error("❌ Migration failed:", error)
    process.exit(1)
  }
}

runSimpleMigration()
