const express = require("express")
const cors = require("cors")
const mysql = require("mysql2/promise")
const bcrypt = require("bcryptjs")
const jwt = require("jsonwebtoken")
const multer = require("multer")
const path = require("path")
const fs = require("fs")
require("dotenv").config()

const app = express()
const PORT = process.env.PORT || 3001

// Middleware
app.use(cors())
app.use(express.json())
app.use("/uploads", express.static("uploads"))

// Create uploads directory if it doesn't exist
if (!fs.existsSync("uploads")) {
  fs.mkdirSync("uploads")
}

// Database connection
const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  user: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "",
  database: process.env.DB_NAME || "otantik_ems",
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
}

const pool = mysql.createPool(dbConfig)

// File upload configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, "uploads/")
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9)
    cb(null, file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname))
  },
})

const upload = multer({ storage })

// JWT middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers["authorization"]
  const token = authHeader && authHeader.split(" ")[1]

  if (!token) {
    return res.status(401).json({ error: "Access token required" })
  }

  jwt.verify(token, process.env.JWT_SECRET || "your-secret-key", (err, user) => {
    if (err) {
      return res.status(403).json({ error: "Invalid token" })
    }
    req.user = user
    next()
  })
}

// Auth Routes
app.post("/api/auth/login", async (req, res) => {
  try {
    const { email, password } = req.body

    const [users] = await pool.execute("SELECT * FROM users WHERE email = ?", [email])

    if (users.length === 0) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    const user = users[0]
    const isValidPassword = await bcrypt.compare(password, user.password)

    if (!isValidPassword) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.first_name,
        lastName: user.last_name,
      },
      process.env.JWT_SECRET || "your-secret-key",
      { expiresIn: "24h" },
    )

    res.json({
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.first_name,
        lastName: user.last_name,
        region: user.region,
      },
    })
  } catch (error) {
    console.error("Login error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/auth/register", async (req, res) => {
  try {
    const { firstName, lastName, email, password, role, region, phone } = req.body

    // Check if user already exists
    const [existingUsers] = await pool.execute("SELECT id FROM users WHERE email = ?", [email])

    if (existingUsers.length > 0) {
      return res.status(400).json({ error: "User already exists" })
    }

    const hashedPassword = await bcrypt.hash(password, 10)

    const [result] = await pool.execute(
      "INSERT INTO users (first_name, last_name, email, password, role, region, phone) VALUES (?, ?, ?, ?, ?, ?, ?)",
      [firstName, lastName, email, hashedPassword, role, region, phone],
    )

    const token = jwt.sign(
      {
        id: result.insertId,
        email,
        role,
        firstName,
        lastName,
      },
      process.env.JWT_SECRET || "your-secret-key",
      { expiresIn: "24h" },
    )

    res.status(201).json({
      token,
      user: {
        id: result.insertId,
        email,
        role,
        firstName,
        lastName,
        region,
      },
    })
  } catch (error) {
    console.error("Registration error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Events Routes
app.get("/api/events", authenticateToken, async (req, res) => {
  try {
    let query = "SELECT * FROM events"
    const params = []

    if (req.user.role === "planner") {
      query += " WHERE planner_id = ?"
      params.push(req.user.id)
    }

    query += " ORDER BY event_date DESC"

    const [events] = await pool.execute(query, params)
    res.json(events)
  } catch (error) {
    console.error("Get events error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/events", authenticateToken, async (req, res) => {
  try {
    const { title, description, event_date, event_time, venue, address, region, expected_guests, budget } = req.body

    const [result] = await pool.execute(
      "INSERT INTO events (title, description, event_date, event_time, venue, address, region, expected_guests, budget, planner_id, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
      [
        title,
        description,
        event_date,
        event_time,
        venue,
        address,
        region,
        expected_guests,
        budget,
        req.user.id,
        "planning",
      ],
    )

    const [newEvent] = await pool.execute("SELECT * FROM events WHERE id = ?", [result.insertId])

    res.status(201).json(newEvent[0])
  } catch (error) {
    console.error("Create event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.get("/api/events/:id", authenticateToken, async (req, res) => {
  try {
    const [events] = await pool.execute("SELECT * FROM events WHERE id = ?", [req.params.id])

    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found" })
    }

    res.json(events[0])
  } catch (error) {
    console.error("Get event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Guests Routes
app.get("/api/events/:eventId/guests", authenticateToken, async (req, res) => {
  try {
    const [guests] = await pool.execute("SELECT * FROM guests WHERE event_id = ? ORDER BY created_at DESC", [
      req.params.eventId,
    ])
    res.json(guests)
  } catch (error) {
    console.error("Get guests error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/events/:eventId/guests", authenticateToken, async (req, res) => {
  try {
    const { name, email, phone, table_number, seat_number, dietary_requirements, plus_one } = req.body

    const [result] = await pool.execute(
      "INSERT INTO guests (event_id, name, email, phone, table_number, seat_number, dietary_requirements, plus_one, rsvp_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
      [req.params.eventId, name, email, phone, table_number, seat_number, dietary_requirements, plus_one, "pending"],
    )

    const [newGuest] = await pool.execute("SELECT * FROM guests WHERE id = ?", [result.insertId])

    res.status(201).json(newGuest[0])
  } catch (error) {
    console.error("Create guest error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Vendors Routes
app.get("/api/vendors", authenticateToken, async (req, res) => {
  try {
    const [vendors] = await pool.execute("SELECT * FROM vendors ORDER BY created_at DESC")
    res.json(vendors)
  } catch (error) {
    console.error("Get vendors error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/vendors", authenticateToken, async (req, res) => {
  try {
    const { business_name, contact_person, email, phone, service_type, region, address, description, website } =
      req.body

    const [result] = await pool.execute(
      "INSERT INTO vendors (business_name, contact_person, email, phone, service_type, region, address, description, website, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
      [business_name, contact_person, email, phone, service_type, region, address, description, website, "active"],
    )

    const [newVendor] = await pool.execute("SELECT * FROM vendors WHERE id = ?", [result.insertId])

    res.status(201).json(newVendor[0])
  } catch (error) {
    console.error("Create vendor error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Users Routes (Admin only)
app.get("/api/users", authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== "admin") {
      return res.status(403).json({ error: "Access denied" })
    }

    const [users] = await pool.execute(
      "SELECT id, first_name, last_name, email, role, region, phone, created_at FROM users ORDER BY created_at DESC",
    )
    res.json(users)
  } catch (error) {
    console.error("Get users error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Dashboard Stats
app.get("/api/dashboard/stats", authenticateToken, async (req, res) => {
  try {
    let stats = {}

    if (req.user.role === "admin") {
      const [eventCount] = await pool.execute("SELECT COUNT(*) as count FROM events")
      const [userCount] = await pool.execute("SELECT COUNT(*) as count FROM users")
      const [vendorCount] = await pool.execute("SELECT COUNT(*) as count FROM vendors")
      const [guestCount] = await pool.execute("SELECT COUNT(*) as count FROM guests")

      stats = {
        totalEvents: eventCount[0].count,
        totalUsers: userCount[0].count,
        totalVendors: vendorCount[0].count,
        totalGuests: guestCount[0].count,
      }
    } else if (req.user.role === "planner") {
      const [eventCount] = await pool.execute("SELECT COUNT(*) as count FROM events WHERE planner_id = ?", [
        req.user.id,
      ])
      const [guestCount] = await pool.execute(
        "SELECT COUNT(*) as count FROM guests g JOIN events e ON g.event_id = e.id WHERE e.planner_id = ?",
        [req.user.id],
      )

      stats = {
        myEvents: eventCount[0].count,
        totalGuests: guestCount[0].count,
      }
    }

    res.json(stats)
  } catch (error) {
    console.error("Get dashboard stats error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// File Upload
app.post("/api/upload", authenticateToken, upload.single("file"), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" })
    }

    res.json({
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      fileUrl: `/uploads/${req.file.filename}`,
    })
  } catch (error) {
    console.error("File upload error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// WhatsApp Integration (Mock for now)
app.post("/api/events/:eventId/send-invitations", authenticateToken, async (req, res) => {
  try {
    const { message, invitationImageUrl } = req.body

    const [guests] = await pool.execute("SELECT * FROM guests WHERE event_id = ? AND phone IS NOT NULL", [
      req.params.eventId,
    ])

    // Mock sending - in real implementation, integrate with WhatsApp Business API
    let successCount = 0
    for (const guest of guests) {
      // Simulate sending
      console.log(`Sending invitation to ${guest.name} at ${guest.phone}`)

      // Update guest record to mark invitation as sent
      await pool.execute("UPDATE guests SET whatsapp_sent = TRUE, whatsapp_sent_at = NOW() WHERE id = ?", [guest.id])

      successCount++
    }

    res.json({
      successCount,
      totalGuests: guests.length,
      message: "Invitations sent successfully",
    })
  } catch (error) {
    console.error("Send invitations error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Health check
app.get("/api/health", (req, res) => {
  res.json({ status: "OK", timestamp: new Date().toISOString() })
})

// Settings Routes
app.get("/api/settings", authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== "admin") {
      return res.status(403).json({ error: "Access denied" })
    }

    const [settings] = await pool.execute(
      "SELECT setting_key, setting_value, setting_type, category, description FROM settings ORDER BY category, setting_key"
    )

    // Group settings by category
    const groupedSettings = settings.reduce((acc, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = {}
      }

      let value = setting.setting_value
      // Convert value based on type
      if (setting.setting_type === 'boolean') {
        value = value === 'true'
      } else if (setting.setting_type === 'number') {
        value = parseFloat(value)
      } else if (setting.setting_type === 'json') {
        try {
          value = JSON.parse(value)
        } catch (e) {
          value = setting.setting_value
        }
      }

      acc[setting.category][setting.setting_key] = {
        value,
        type: setting.setting_type,
        description: setting.description
      }
      return acc
    }, {})

    res.json(groupedSettings)
  } catch (error) {
    console.error("Get settings error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.put("/api/settings", authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== "admin") {
      return res.status(403).json({ error: "Access denied" })
    }

    const { settings } = req.body

    // Update settings in database
    for (const [key, value] of Object.entries(settings)) {
      let stringValue = value
      if (typeof value === 'boolean') {
        stringValue = value.toString()
      } else if (typeof value === 'object') {
        stringValue = JSON.stringify(value)
      } else {
        stringValue = value.toString()
      }

      await pool.execute(
        "UPDATE settings SET setting_value = ?, updated_at = CURRENT_TIMESTAMP WHERE setting_key = ?",
        [stringValue, key]
      )
    }

    res.json({ message: "Settings updated successfully" })
  } catch (error) {
    console.error("Update settings error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Profit Tracking Routes
app.get("/api/profit", authenticateToken, async (req, res) => {
  try {
    let query = `
      SELECT
        pt.*,
        e.title as event_title,
        e.event_date,
        e.venue,
        u.first_name,
        u.last_name
      FROM profit_tracking pt
      JOIN events e ON pt.event_id = e.id
      LEFT JOIN users u ON e.planner_id = u.id
    `
    const params = []

    if (req.user.role === "planner") {
      query += " WHERE e.planner_id = ?"
      params.push(req.user.id)
    }

    query += " ORDER BY e.event_date DESC"

    const [profits] = await pool.execute(query, params)
    res.json(profits)
  } catch (error) {
    console.error("Get profit data error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.get("/api/profit/:eventId", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params

    // Check if user has access to this event
    let eventQuery = "SELECT * FROM events WHERE id = ?"
    const eventParams = [eventId]

    if (req.user.role === "planner") {
      eventQuery += " AND planner_id = ?"
      eventParams.push(req.user.id)
    }

    const [events] = await pool.execute(eventQuery, eventParams)
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found or access denied" })
    }

    // Get profit data
    const [profits] = await pool.execute(
      "SELECT * FROM profit_tracking WHERE event_id = ?",
      [eventId]
    )

    // Get expenses
    const [expenses] = await pool.execute(`
      SELECT
        ee.*,
        v.business_name as vendor_name,
        u.first_name,
        u.last_name
      FROM event_expenses ee
      LEFT JOIN vendors v ON ee.vendor_id = v.id
      LEFT JOIN users u ON ee.created_by = u.id
      WHERE ee.event_id = ?
      ORDER BY ee.expense_date DESC
    `, [eventId])

    res.json({
      event: events[0],
      profit: profits[0] || null,
      expenses
    })
  } catch (error) {
    console.error("Get event profit error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.put("/api/profit/:eventId", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params
    const { revenue, expenses, notes } = req.body

    // Check if user has access to this event
    let eventQuery = "SELECT * FROM events WHERE id = ?"
    const eventParams = [eventId]

    if (req.user.role === "planner") {
      eventQuery += " AND planner_id = ?"
      eventParams.push(req.user.id)
    }

    const [events] = await pool.execute(eventQuery, eventParams)
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found or access denied" })
    }

    // Update or insert profit data
    await pool.execute(`
      INSERT INTO profit_tracking (event_id, revenue, expenses, notes)
      VALUES (?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        revenue = VALUES(revenue),
        expenses = VALUES(expenses),
        notes = VALUES(notes),
        updated_at = CURRENT_TIMESTAMP
    `, [eventId, revenue || 0, expenses || 0, notes || ''])

    // Get updated profit data
    const [profits] = await pool.execute(
      "SELECT * FROM profit_tracking WHERE event_id = ?",
      [eventId]
    )

    res.json(profits[0])
  } catch (error) {
    console.error("Update profit error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Event Expenses Routes
app.post("/api/events/:eventId/expenses", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params
    const { expense_category, description, amount, expense_date, vendor_id, receipt_url } = req.body

    // Check if user has access to this event
    let eventQuery = "SELECT * FROM events WHERE id = ?"
    const eventParams = [eventId]

    if (req.user.role === "planner") {
      eventQuery += " AND planner_id = ?"
      eventParams.push(req.user.id)
    }

    const [events] = await pool.execute(eventQuery, eventParams)
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found or access denied" })
    }

    // Insert expense
    const [result] = await pool.execute(`
      INSERT INTO event_expenses
      (event_id, expense_category, description, amount, expense_date, vendor_id, receipt_url, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [eventId, expense_category, description, amount, expense_date, vendor_id, receipt_url, req.user.id])

    // Update profit tracking expenses
    const [totalExpenses] = await pool.execute(
      "SELECT SUM(amount) as total FROM event_expenses WHERE event_id = ? AND status != 'cancelled'",
      [eventId]
    )

    await pool.execute(`
      INSERT INTO profit_tracking (event_id, expenses)
      VALUES (?, ?)
      ON DUPLICATE KEY UPDATE
        expenses = VALUES(expenses),
        updated_at = CURRENT_TIMESTAMP
    `, [eventId, totalExpenses[0].total || 0])

    res.json({ id: result.insertId, message: "Expense added successfully" })
  } catch (error) {
    console.error("Add expense error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.put("/api/expenses/:expenseId", authenticateToken, async (req, res) => {
  try {
    const { expenseId } = req.params
    const { expense_category, description, amount, expense_date, vendor_id, receipt_url, status } = req.body

    // Get expense and check access
    const [expenses] = await pool.execute(`
      SELECT ee.*, e.planner_id
      FROM event_expenses ee
      JOIN events e ON ee.event_id = e.id
      WHERE ee.id = ?
    `, [expenseId])

    if (expenses.length === 0) {
      return res.status(404).json({ error: "Expense not found" })
    }

    const expense = expenses[0]
    if (req.user.role === "planner" && expense.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Update expense
    await pool.execute(`
      UPDATE event_expenses
      SET expense_category = ?, description = ?, amount = ?, expense_date = ?,
          vendor_id = ?, receipt_url = ?, status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [expense_category, description, amount, expense_date, vendor_id, receipt_url, status, expenseId])

    // Update profit tracking expenses
    const [totalExpenses] = await pool.execute(
      "SELECT SUM(amount) as total FROM event_expenses WHERE event_id = ? AND status != 'cancelled'",
      [expense.event_id]
    )

    await pool.execute(`
      UPDATE profit_tracking
      SET expenses = ?, updated_at = CURRENT_TIMESTAMP
      WHERE event_id = ?
    `, [totalExpenses[0].total || 0, expense.event_id])

    res.json({ message: "Expense updated successfully" })
  } catch (error) {
    console.error("Update expense error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.delete("/api/expenses/:expenseId", authenticateToken, async (req, res) => {
  try {
    const { expenseId } = req.params

    // Get expense and check access
    const [expenses] = await pool.execute(`
      SELECT ee.*, e.planner_id
      FROM event_expenses ee
      JOIN events e ON ee.event_id = e.id
      WHERE ee.id = ?
    `, [expenseId])

    if (expenses.length === 0) {
      return res.status(404).json({ error: "Expense not found" })
    }

    const expense = expenses[0]
    if (req.user.role === "planner" && expense.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Delete expense
    await pool.execute("DELETE FROM event_expenses WHERE id = ?", [expenseId])

    // Update profit tracking expenses
    const [totalExpenses] = await pool.execute(
      "SELECT SUM(amount) as total FROM event_expenses WHERE event_id = ? AND status != 'cancelled'",
      [expense.event_id]
    )

    await pool.execute(`
      UPDATE profit_tracking
      SET expenses = ?, updated_at = CURRENT_TIMESTAMP
      WHERE event_id = ?
    `, [totalExpenses[0].total || 0, expense.event_id])

    res.json({ message: "Expense deleted successfully" })
  } catch (error) {
    console.error("Delete expense error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Profit Analytics Routes
app.get("/api/analytics/profit", authenticateToken, async (req, res) => {
  try {
    const { timeRange = '6months' } = req.query

    let dateFilter = ""
    switch (timeRange) {
      case '1month':
        dateFilter = "AND e.event_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)"
        break
      case '3months':
        dateFilter = "AND e.event_date >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)"
        break
      case '6months':
        dateFilter = "AND e.event_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)"
        break
      case '1year':
        dateFilter = "AND e.event_date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)"
        break
    }

    let query = `
      SELECT
        DATE_FORMAT(e.event_date, '%Y-%m') as month,
        COUNT(e.id) as total_events,
        SUM(pt.revenue) as total_revenue,
        SUM(pt.expenses) as total_expenses,
        SUM(pt.profit) as total_profit,
        AVG(pt.profit_margin) as avg_profit_margin
      FROM events e
      LEFT JOIN profit_tracking pt ON e.id = pt.event_id
      WHERE 1=1 ${dateFilter}
    `
    const params = []

    if (req.user.role === "planner") {
      query += " AND e.planner_id = ?"
      params.push(req.user.id)
    }

    query += " GROUP BY DATE_FORMAT(e.event_date, '%Y-%m') ORDER BY month DESC"

    const [analytics] = await pool.execute(query, params)
    res.json(analytics)
  } catch (error) {
    console.error("Get profit analytics error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({ error: "Something went wrong!" })
})

// Profit Management Routes - Working with real data
// Get profit data for all events
app.get("/api/profit", authenticateToken, async (req, res) => {
  try {
    let query = `
      SELECT
        e.id as event_id,
        e.title as event_title,
        e.event_date,
        e.venue,
        e.budget,
        e.status,
        u.first_name,
        u.last_name,
        COALESCE(pt.revenue, e.budget, 0) as revenue,
        COALESCE(pt.expenses, 0) as expenses,
        COALESCE(pt.profit, (e.budget - COALESCE(pt.expenses, 0)), 0) as profit,
        COALESCE(pt.profit_margin,
          CASE
            WHEN e.budget > 0 THEN ((e.budget - COALESCE(pt.expenses, 0)) / e.budget) * 100
            ELSE 0
          END
        ) as profit_margin,
        pt.notes
      FROM events e
      LEFT JOIN profit_tracking pt ON e.id = pt.event_id
      LEFT JOIN users u ON e.planner_id = u.id
    `
    const params = []

    if (req.user.role === "planner") {
      query += " WHERE e.planner_id = ?"
      params.push(req.user.id)
    }

    query += " ORDER BY e.event_date DESC"

    const [profits] = await pool.execute(query, params)
    res.json(profits)
  } catch (error) {
    console.error("Get profit data error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Update profit information for a specific event
app.put("/api/profit/:eventId", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params
    const { revenue, expenses, notes } = req.body

    // Check if user has access to this event
    let eventQuery = "SELECT * FROM events WHERE id = ?"
    const eventParams = [eventId]

    if (req.user.role === "planner") {
      eventQuery += " AND planner_id = ?"
      eventParams.push(req.user.id)
    }

    const [events] = await pool.execute(eventQuery, eventParams)
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found or access denied" })
    }

    // Check if profit tracking record exists
    const [existingProfit] = await pool.execute(
      "SELECT id FROM profit_tracking WHERE event_id = ? LIMIT 1",
      [eventId]
    )

    if (existingProfit.length > 0) {
      // Update existing record
      await pool.execute(`
        UPDATE profit_tracking
        SET revenue = ?, expenses = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE event_id = ?
      `, [revenue || 0, expenses || 0, notes || '', eventId])
    } else {
      // Insert new record
      await pool.execute(`
        INSERT INTO profit_tracking (event_id, revenue, expenses, notes)
        VALUES (?, ?, ?, ?)
      `, [eventId, revenue || 0, expenses || 0, notes || ''])
    }

    res.json({
      message: "Profit data updated successfully",
      event_id: eventId,
      revenue: revenue,
      expenses: expenses,
      profit: (revenue || 0) - (expenses || 0),
      notes: notes
    })
  } catch (error) {
    console.error("Update profit error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Get detailed profit information for a specific event
app.get("/api/profit/:eventId", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params

    // Check if user has access to this event
    let eventQuery = "SELECT * FROM events WHERE id = ?"
    const eventParams = [eventId]

    if (req.user.role === "planner") {
      eventQuery += " AND planner_id = ?"
      eventParams.push(req.user.id)
    }

    const [events] = await pool.execute(eventQuery, eventParams)
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found or access denied" })
    }

    const event = events[0]

    // Get profit tracking data if it exists
    const [profits] = await pool.execute(
      "SELECT * FROM profit_tracking WHERE event_id = ?",
      [eventId]
    )

    const profitData = profits[0] || {
      revenue: event.budget || 0,
      expenses: 0,
      profit: event.budget || 0,
      profit_margin: 100,
      notes: ''
    }

    res.json({
      event: event,
      profit: profitData,
      calculated_profit: (profitData.revenue || 0) - (profitData.expenses || 0),
      calculated_margin: profitData.revenue > 0 ?
        (((profitData.revenue || 0) - (profitData.expenses || 0)) / profitData.revenue) * 100 : 0
    })
  } catch (error) {
    console.error("Get event profit error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Clear temporary profit data (admin only)
app.delete("/api/profit/clear-temp", authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== "admin") {
      return res.status(403).json({ error: "Admin access required" })
    }

    // Clear all temporary profit tracking data
    await pool.execute("DELETE FROM profit_tracking")

    res.json({ message: "Temporary profit data cleared successfully" })
  } catch (error) {
    console.error("Clear temp data error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.get("/api/profit/:eventId", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params

    // Check if user has access to this event
    let eventQuery = "SELECT * FROM events WHERE id = ?"
    const eventParams = [eventId]

    if (req.user.role === "planner") {
      eventQuery += " AND planner_id = ?"
      eventParams.push(req.user.id)
    }

    const [events] = await pool.execute(eventQuery, eventParams)
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found or access denied" })
    }

    // Get expenses for this event
    const [expenses] = await pool.execute(`
      SELECT
        ee.*,
        v.business_name as vendor_name
      FROM event_expenses ee
      LEFT JOIN vendors v ON ee.vendor_id = v.id
      WHERE ee.event_id = ?
      ORDER BY ee.expense_date DESC
    `, [eventId])

    // Calculate totals
    const totalExpenses = expenses.reduce((sum, expense) =>
      expense.status !== 'cancelled' ? sum + parseFloat(expense.amount) : sum, 0
    )

    const event = events[0]
    const revenue = parseFloat(event.budget) || 0
    const profit = revenue - totalExpenses
    const profitMargin = revenue > 0 ? (profit / revenue) * 100 : 0

    res.json({
      event: event,
      expenses: expenses,
      summary: {
        revenue: revenue,
        total_expenses: totalExpenses,
        profit: profit,
        profit_margin: profitMargin
      }
    })
  } catch (error) {
    console.error("Get event profit error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Add expense to event
app.post("/api/events/:eventId/expenses", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params
    const { category, description, amount, expense_date, vendor_id } = req.body

    // Check if user has access to this event
    let eventQuery = "SELECT * FROM events WHERE id = ?"
    const eventParams = [eventId]

    if (req.user.role === "planner") {
      eventQuery += " AND planner_id = ?"
      eventParams.push(req.user.id)
    }

    const [events] = await pool.execute(eventQuery, eventParams)
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found or access denied" })
    }

    // Insert expense
    const [result] = await pool.execute(`
      INSERT INTO event_expenses
      (event_id, expense_category, description, amount, expense_date, vendor_id, created_by, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')
    `, [eventId, category, description, amount, expense_date, vendor_id, req.user.id])

    res.json({
      id: result.insertId,
      message: "Expense added successfully"
    })
  } catch (error) {
    console.error("Add expense error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Update expense
app.put("/api/expenses/:expenseId", authenticateToken, async (req, res) => {
  try {
    const { expenseId } = req.params
    const { category, description, amount, expense_date, vendor_id, status } = req.body

    // Get expense and check access
    const [expenses] = await pool.execute(`
      SELECT ee.*, e.planner_id
      FROM event_expenses ee
      JOIN events e ON ee.event_id = e.id
      WHERE ee.id = ?
    `, [expenseId])

    if (expenses.length === 0) {
      return res.status(404).json({ error: "Expense not found" })
    }

    const expense = expenses[0]
    if (req.user.role === "planner" && expense.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Update expense
    await pool.execute(`
      UPDATE event_expenses
      SET expense_category = ?, description = ?, amount = ?, expense_date = ?,
          vendor_id = ?, status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [category, description, amount, expense_date, vendor_id, status, expenseId])

    res.json({ message: "Expense updated successfully" })
  } catch (error) {
    console.error("Update expense error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Delete expense
app.delete("/api/expenses/:expenseId", authenticateToken, async (req, res) => {
  try {
    const { expenseId } = req.params

    // Get expense and check access
    const [expenses] = await pool.execute(`
      SELECT ee.*, e.planner_id
      FROM event_expenses ee
      JOIN events e ON ee.event_id = e.id
      WHERE ee.id = ?
    `, [expenseId])

    if (expenses.length === 0) {
      return res.status(404).json({ error: "Expense not found" })
    }

    const expense = expenses[0]
    if (req.user.role === "planner" && expense.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Delete expense
    await pool.execute("DELETE FROM event_expenses WHERE id = ?", [expenseId])

    res.json({ message: "Expense deleted successfully" })
  } catch (error) {
    console.error("Delete expense error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Update event budget/revenue
app.put("/api/events/:eventId/budget", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params
    const { budget } = req.body

    // Check if user has access to this event
    let eventQuery = "SELECT * FROM events WHERE id = ?"
    const eventParams = [eventId]

    if (req.user.role === "planner") {
      eventQuery += " AND planner_id = ?"
      eventParams.push(req.user.id)
    }

    const [events] = await pool.execute(eventQuery, eventParams)
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found or access denied" })
    }

    // Update budget
    await pool.execute(
      "UPDATE events SET budget = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
      [budget, eventId]
    )

    res.json({ message: "Budget updated successfully" })
  } catch (error) {
    console.error("Update budget error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})



// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
})
