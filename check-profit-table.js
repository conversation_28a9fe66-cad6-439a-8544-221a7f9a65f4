const mysql = require("mysql2/promise")
require("dotenv").config()

async function checkProfitTable() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: "otantik_ems",
  })

  try {
    console.log("Checking profit_tracking table structure...")
    
    // Show table structure
    const [structure] = await connection.execute("DESCRIBE profit_tracking")
    console.log("Table structure:", structure)
    
    // Check for unique constraints
    const [indexes] = await connection.execute("SHOW INDEX FROM profit_tracking")
    console.log("Indexes:", indexes)
    
    // Try to update event 1 directly
    console.log("\nTrying direct update...")
    const [result] = await connection.execute(`
      UPDATE profit_tracking 
      SET revenue = 20000000, expenses = 12000000, notes = 'Direct update test'
      WHERE event_id = 1
    `)
    console.log("Update result:", result)
    
    // Check the data
    const [data] = await connection.execute("SELECT * FROM profit_tracking WHERE event_id = 1")
    console.log("Updated data:", data)

    await connection.end()
  } catch (error) {
    console.error("Error:", error)
  }
}

checkProfitTable()
