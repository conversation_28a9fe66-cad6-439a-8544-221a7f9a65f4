"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { AdminSidebar } from "@/components/admin-sidebar"
import { profitService, ProfitData } from "@/lib/services/profit.service"
import { DollarSign, TrendingUp, TrendingDown, Calendar, Loader2, Eye } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"

export default function AdminProfitPage() {
  const [profitData, setProfitData] = useState<ProfitData[]>([])
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'1month' | '3months' | '6months' | '1year'>('6months')

  useEffect(() => {
    loadProfitData()
  }, [])

  const loadProfitData = async () => {
    try {
      setLoading(true)
      const data = await profitService.getAllProfitData()
      setProfitData(data)
    } catch (error) {
      console.error("Failed to load profit data:", error)
      toast.error("Failed to load profit data")
    } finally {
      setLoading(false)
    }
  }

  const calculateTotals = () => {
    return profitData.reduce(
      (acc, item) => ({
        revenue: acc.revenue + parseFloat(item.revenue),
        expenses: acc.expenses + parseFloat(item.expenses),
        profit: acc.profit + parseFloat(item.profit),
      }),
      { revenue: 0, expenses: 0, profit: 0 }
    )
  }

  const totals = calculateTotals()
  const avgProfitMargin = totals.revenue > 0 ? ((totals.profit / totals.revenue) * 100) : 0

  const getProfitStatusBadge = (margin: string) => {
    const marginNum = parseFloat(margin)
    if (marginNum >= 30) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>
    if (marginNum >= 20) return <Badge className="bg-blue-100 text-blue-800">Good</Badge>
    if (marginNum >= 10) return <Badge className="bg-yellow-100 text-yellow-800">Average</Badge>
    return <Badge className="bg-red-100 text-red-800">Poor</Badge>
  }

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <AdminSidebar />
        <div className="flex-1 flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <AdminSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Profit Management</h1>
            <p className="text-gray-600">Track revenue, expenses, and profitability across all events</p>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{profitService.formatCurrency(totals.revenue)}</div>
                <p className="text-xs text-muted-foreground">Across {profitData.length} events</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
                <TrendingDown className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{profitService.formatCurrency(totals.expenses)}</div>
                <p className="text-xs text-muted-foreground">Operating costs</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{profitService.formatCurrency(totals.profit)}</div>
                <p className="text-xs text-muted-foreground">
                  {avgProfitMargin.toFixed(1)}% margin
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Events</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{profitData.length}</div>
                <p className="text-xs text-muted-foreground">Total events tracked</p>
              </CardContent>
            </Card>
          </div>

          {/* Events Profit Table */}
          <Card>
            <CardHeader>
              <CardTitle>Event Profitability</CardTitle>
              <CardDescription>Detailed profit breakdown for each event</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">Event</th>
                      <th className="text-left py-2">Date</th>
                      <th className="text-right py-2">Revenue</th>
                      <th className="text-right py-2">Expenses</th>
                      <th className="text-right py-2">Profit</th>
                      <th className="text-center py-2">Margin</th>
                      <th className="text-center py-2">Status</th>
                      <th className="text-center py-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {profitData.map((item) => (
                      <tr key={item.id} className="border-b hover:bg-gray-50">
                        <td className="py-3">
                          <div>
                            <div className="font-medium">{item.event_title}</div>
                            <div className="text-sm text-gray-500">{item.venue}</div>
                          </div>
                        </td>
                        <td className="py-3">
                          {new Date(item.event_date || '').toLocaleDateString()}
                        </td>
                        <td className="py-3 text-right font-medium">
                          {profitService.formatCurrency(item.revenue)}
                        </td>
                        <td className="py-3 text-right">
                          {profitService.formatCurrency(item.expenses)}
                        </td>
                        <td className="py-3 text-right font-medium">
                          {profitService.formatCurrency(item.profit)}
                        </td>
                        <td className="py-3 text-center">
                          {parseFloat(item.profit_margin).toFixed(1)}%
                        </td>
                        <td className="py-3 text-center">
                          {getProfitStatusBadge(item.profit_margin)}
                        </td>
                        <td className="py-3 text-center">
                          <Link href={`/admin/profit/${item.event_id}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
