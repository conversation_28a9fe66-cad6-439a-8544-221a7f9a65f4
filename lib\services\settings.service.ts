import apiClient from "@/lib/api"

export interface Setting {
  value: any
  type: 'string' | 'number' | 'boolean' | 'json'
  description: string
}

export interface SettingsGroup {
  [key: string]: Setting
}

export interface AllSettings {
  general: SettingsGroup
  security: SettingsGroup
  notifications: SettingsGroup
  regional: SettingsGroup
  database: SettingsGroup
  profit: SettingsGroup
}

class SettingsService {
  async getSettings(): Promise<AllSettings> {
    return apiClient.request("/settings")
  }

  async updateSettings(settings: { [key: string]: any }): Promise<{ message: string }> {
    return apiClient.request("/settings", {
      method: "PUT",
      body: JSON.stringify({ settings }),
    })
  }

  async getSetting(key: string): Promise<any> {
    const allSettings = await this.getSettings()
    
    // Search through all categories for the setting
    for (const category of Object.values(allSettings)) {
      if (category[key]) {
        return category[key].value
      }
    }
    
    return null
  }

  async updateSetting(key: string, value: any): Promise<{ message: string }> {
    return this.updateSettings({ [key]: value })
  }

  // Helper methods for specific setting categories
  async getGeneralSettings(): Promise<SettingsGroup> {
    const settings = await this.getSettings()
    return settings.general
  }

  async getSecuritySettings(): Promise<SettingsGroup> {
    const settings = await this.getSettings()
    return settings.security
  }

  async getNotificationSettings(): Promise<SettingsGroup> {
    const settings = await this.getSettings()
    return settings.notifications
  }

  async getRegionalSettings(): Promise<SettingsGroup> {
    const settings = await this.getSettings()
    return settings.regional
  }

  async getDatabaseSettings(): Promise<SettingsGroup> {
    const settings = await this.getSettings()
    return settings.database
  }

  async getProfitSettings(): Promise<SettingsGroup> {
    const settings = await this.getSettings()
    return settings.profit
  }
}

export const settingsService = new SettingsService()
