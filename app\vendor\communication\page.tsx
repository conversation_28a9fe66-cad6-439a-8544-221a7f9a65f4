"use client"

import { <PERSON>, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { MessageCircle, Send, Phone, Mail, Calendar } from "lucide-react"
import { VendorSidebar } from "@/components/vendor-sidebar"

const conversations = [
  {
    id: 1,
    clientName: "Bimm Audrey",
    eventName: "Ngounou Arlane & Otantik Nuna Wedding",
    lastMessage: "Can you send the preview photos by tomorrow?",
    time: "2 hours ago",
    unread: 2,
    status: "active",
  },
  {
    id: 2,
    clientName: "Tantoh Emmanuel",
    eventName: "Cameroon Tech Summit 2024",
    lastMessage: "Thank you for the great work!",
    time: "1 day ago",
    unread: 0,
    status: "completed",
  },
  {
    id: 3,
    clientName: "<PERSON>",
    eventName: "Birthday Celebration",
    lastMessage: "When can we schedule the photo session?",
    time: "3 days ago",
    unread: 1,
    status: "planning",
  },
]

export default function VendorCommunication() {
  return (
    <div className="flex h-screen bg-gray-50">
      <VendorSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Client Communication</h1>
            <p className="text-gray-600">Communicate with event planners and clients</p>
          </div>
        </div>

        <div className="p-6">
          <div className="grid lg:grid-cols-3 gap-6">
            {/* Conversations List */}
            <div className="lg:col-span-1">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageCircle className="h-5 w-5 text-blue-600" />
                    Conversations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {conversations.map((conversation) => (
                      <div
                        key={conversation.id}
                        className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                      >
                        <div className="flex items-start gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback>
                              {conversation.clientName
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="font-medium text-sm truncate">{conversation.clientName}</h4>
                              {conversation.unread > 0 && (
                                <Badge variant="default" className="bg-blue-100 text-blue-800 text-xs">
                                  {conversation.unread}
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs text-gray-600 mb-1 truncate">{conversation.eventName}</p>
                            <p className="text-xs text-gray-500 truncate">{conversation.lastMessage}</p>
                            <p className="text-xs text-gray-400 mt-1">{conversation.time}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Chat Interface */}
            <div className="lg:col-span-2">
              <Card className="border-0 shadow-lg h-full">
                <CardHeader className="border-b">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarFallback>BA</AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg">Bimm Audrey</CardTitle>
                        <CardDescription>Ngounou Arlane & Otantik Nuna Wedding</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Mail className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Calendar className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="flex-1 p-0">
                  {/* Messages */}
                  <div className="h-96 overflow-y-auto p-4 space-y-4">
                    <div className="flex justify-start">
                      <div className="bg-gray-100 rounded-lg p-3 max-w-xs">
                        <p className="text-sm">Hi! How are the preparations going for the wedding photos?</p>
                        <p className="text-xs text-gray-500 mt-1">10:30 AM</p>
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <div className="bg-green-600 text-white rounded-lg p-3 max-w-xs">
                        <p className="text-sm">Everything is on track! I'll have the equipment ready by Friday.</p>
                        <p className="text-xs text-green-100 mt-1">10:35 AM</p>
                      </div>
                    </div>
                    <div className="flex justify-start">
                      <div className="bg-gray-100 rounded-lg p-3 max-w-xs">
                        <p className="text-sm">Can you send the preview photos by tomorrow?</p>
                        <p className="text-xs text-gray-500 mt-1">2 hours ago</p>
                      </div>
                    </div>
                  </div>

                  {/* Message Input */}
                  <div className="border-t p-4">
                    <div className="flex items-center gap-2">
                      <Textarea
                        placeholder="Type your message..."
                        className="flex-1 min-h-[40px] max-h-[120px]"
                        rows={1}
                      />
                      <Button className="bg-gradient-to-r from-green-600 to-emerald-600">
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
