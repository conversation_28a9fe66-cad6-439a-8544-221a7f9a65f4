"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { QrCode, Calendar, MapPin, Users, Download, Heart, Clock, CheckCircle } from "lucide-react"

const guestData = {
  name: "Ngounou Arlane",
  email: "<EMAIL>",
  invitationCode: "WED-2024-001",
  event: {
    name: "Otantik Nuna & Bimm Audrey Wedding",
    date: "December 15, 2024",
    time: "6:00 PM",
    venue: "Hilton Hotel Yaoundé",
    address: "Boulevard du 20 Mai, Yaoundé, Centre Region",
    dresscode: "Formal/Traditional",
    table: "Table 5",
    seat: "Seat 3",
  },
  rsvpStatus: "confirmed",
  checkedIn: false,
  plusOne: true,
}

const eventPhotos = [
  { id: 1, url: "/placeholder.svg?height=200&width=300", caption: "Ceremony moments" },
  { id: 2, url: "/placeholder.svg?height=200&width=300", caption: "Traditional dance" },
  { id: 3, url: "/placeholder.svg?height=200&width=300", caption: "Family photos" },
  { id: 4, url: "/placeholder.svg?height=200&width=300", caption: "Reception dinner" },
]

export default function GuestDashboard() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Welcome, {guestData.name}!</h1>
            <p className="text-gray-600">Your event dashboard</p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="px-3 py-1">
              <QrCode className="h-4 w-4 mr-1" />
              {guestData.invitationCode}
            </Badge>
            {guestData.rsvpStatus === "confirmed" && (
              <Badge className="bg-green-100 text-green-800">
                <CheckCircle className="h-4 w-4 mr-1" />
                RSVP Confirmed
              </Badge>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* Event Info Card */}
        <Card className="border-0 shadow-2xl bg-gradient-to-br from-amber-500 to-yellow-500 text-white">
          <CardContent className="p-8 text-center">
            <div className="mb-6">
              <Heart className="h-12 w-12 mx-auto mb-4 opacity-80" />
              <h2 className="text-3xl font-bold mb-2">You're Invited!</h2>
            </div>

            <div className="space-y-4 text-lg opacity-90">
              <h3 className="text-2xl font-semibold">{guestData.event.name}</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2">
                  <Calendar className="h-5 w-5" />
                  <span>{guestData.event.date}</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <Clock className="h-5 w-5" />
                  <span>{guestData.event.time}</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <MapPin className="h-5 w-5" />
                  <span>{guestData.event.venue}</span>
                </div>
              </div>
            </div>

            <div className="mt-8 p-6 bg-white/20 rounded-lg">
              <p className="text-sm leading-relaxed">
                Join us as we celebrate this special day with family and friends. Your presence would make our joy
                complete.
              </p>
            </div>

            <div className="mt-8">
              <div className="bg-white p-4 rounded-lg inline-block">
                <QrCode className="h-24 w-24 text-gray-800 mx-auto" />
              </div>
              <p className="text-sm mt-3 opacity-80">Scan this QR code at the venue to check in</p>
            </div>
          </CardContent>
        </Card>

        {/* Quick Info Grid */}
        <div className="grid md:grid-cols-2 gap-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Event Details</CardTitle>
              <CardDescription>Everything you need to know</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-amber-600" />
                <div>
                  <p className="font-medium">{guestData.event.date}</p>
                  <p className="text-sm text-gray-600">{guestData.event.time}</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-amber-600 mt-1" />
                <div>
                  <p className="font-medium">{guestData.event.venue}</p>
                  <p className="text-sm text-gray-600">{guestData.event.address}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Users className="h-5 w-5 text-amber-600" />
                <div>
                  <p className="font-medium">Dress Code</p>
                  <p className="text-sm text-gray-600">{guestData.event.dresscode}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Your Seating</CardTitle>
              <CardDescription>Your assigned table and seat</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center p-6 bg-amber-50 rounded-lg">
                <div className="text-3xl font-bold text-amber-600 mb-2">{guestData.event.table}</div>
                <div className="text-lg font-medium text-gray-900">{guestData.event.seat}</div>
                {guestData.plusOne && (
                  <Badge variant="outline" className="mt-2">
                    Plus One Included
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Event Photos */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Event Photos</CardTitle>
                <CardDescription>Beautiful moments from the event</CardDescription>
              </div>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {eventPhotos.map((photo) => (
                <div key={photo.id} className="group relative overflow-hidden rounded-lg">
                  <img
                    src={photo.url || "/placeholder.svg"}
                    alt={photo.caption}
                    className="w-full h-32 object-cover transition-transform group-hover:scale-105"
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2">
                    <p className="text-white text-xs">{photo.caption}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
