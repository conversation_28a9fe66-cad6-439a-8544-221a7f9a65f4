"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { QrCode, Download, Search, Users, Mail, Send, Eye } from "lucide-react"
import { PlannerSidebar } from "@/components/planner-sidebar"

const guestQRCodes = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON><PERSON> Arl<PERSON>",
    email: "<EMAIL>",
    table: "Table 5",
    qrCode: "WED-2024-001-NA",
    generated: true,
    sent: true,
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    table: "Table 5",
    qrCode: "WED-2024-002-ON",
    generated: true,
    sent: true,
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    table: "Table 8",
    qrCode: "WED-2024-003-AM",
    generated: true,
    sent: false,
  },
  {
    id: 4,
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    table: "Table 12",
    qrCode: "WED-2024-004-TE",
    generated: false,
    sent: false,
  },
]

export default function PlannerQRCodes() {
  const [searchTerm, setSearchTerm] = useState("")

  const filteredGuests = guestQRCodes.filter(
    (guest) =>
      guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.email.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="flex h-screen bg-gray-50">
      <PlannerSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">QR Codes</h1>
              <p className="text-gray-600">Generate and manage guest QR codes</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download All
              </Button>
              <Button className="bg-gradient-to-r from-blue-600 to-cyan-600">
                <QrCode className="h-4 w-4 mr-2" />
                Generate All QR Codes
              </Button>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* QR Code Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Guests</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">{guestQRCodes.length}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Generated</p>
                    <p className="text-3xl font-bold text-green-600 mt-2">
                      {guestQRCodes.filter((g) => g.generated).length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <QrCode className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Sent</p>
                    <p className="text-3xl font-bold text-amber-600 mt-2">
                      {guestQRCodes.filter((g) => g.sent).length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center">
                    <Mail className="h-6 w-6 text-amber-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-red-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-3xl font-bold text-red-600 mt-2">
                      {guestQRCodes.filter((g) => !g.generated).length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <QrCode className="h-6 w-6 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* QR Code Management */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <QrCode className="h-5 w-5 text-blue-600" />
                    Guest QR Codes
                  </CardTitle>
                  <CardDescription>Generate and manage individual guest QR codes</CardDescription>
                </div>
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search guests..."
                      className="pl-10 w-80"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredGuests.map((guest) => (
                  <div key={guest.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="font-semibold">{guest.name}</h3>
                        <p className="text-sm text-gray-600">{guest.email}</p>
                        <Badge variant="outline" className="mt-1">
                          {guest.table}
                        </Badge>
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        {guest.generated ? (
                          <Badge className="bg-green-100 text-green-800">Generated</Badge>
                        ) : (
                          <Badge variant="secondary">Pending</Badge>
                        )}
                        {guest.sent && <Badge className="bg-blue-100 text-blue-800">Sent</Badge>}
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4 mb-4 text-center">
                      {guest.generated ? (
                        <div>
                          <QrCode className="h-16 w-16 mx-auto mb-2 text-gray-600" />
                          <p className="text-xs text-gray-500 font-mono">{guest.qrCode}</p>
                        </div>
                      ) : (
                        <div>
                          <div className="h-16 w-16 mx-auto mb-2 bg-gray-200 rounded flex items-center justify-center">
                            <QrCode className="h-8 w-8 text-gray-400" />
                          </div>
                          <p className="text-xs text-gray-500">Not generated</p>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      {guest.generated ? (
                        <>
                          <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                            <Download className="h-4 w-4 mr-1" />
                            Download
                          </Button>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                          {!guest.sent && (
                            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                              <Send className="h-4 w-4 mr-1" />
                              Send
                            </Button>
                          )}
                        </>
                      ) : (
                        <Button size="sm" className="w-full bg-green-600 hover:bg-green-700">
                          <QrCode className="h-4 w-4 mr-1" />
                          Generate QR
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
