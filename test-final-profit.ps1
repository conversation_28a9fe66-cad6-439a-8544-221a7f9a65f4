# Test the final profit functionality
$loginBody = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    Write-Host "=== Testing Final Profit Management Backend ==="
    
    # Login
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json" -UseBasicParsing
    $loginData = $loginResponse.Content | ConvertFrom-Json
    $token = $loginData.token
    
    Write-Host "✅ Login successful!"
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # Test 1: Update profit data for event 2
    Write-Host "`n--- Test 1: Update Profit Data for Event 2 ---"
    $updateBody = @{
        revenue = 30000000
        expenses = 15000000
        notes = "Updated with real profit data from backend API"
    } | ConvertTo-Json
    
    $updateResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit/2" -Method PUT -Body $updateBody -Headers $headers -UseBasicParsing
    Write-Host "Status: $($updateResponse.StatusCode)"
    Write-Host "Response: $($updateResponse.Content)"
    
    # Test 2: Get all profit data to see the changes
    Write-Host "`n--- Test 2: Get All Profit Data ---"
    $profitResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Status: $($profitResponse.StatusCode)"
    $profitData = $profitResponse.Content | ConvertFrom-Json
    Write-Host "Events with profit data:"
    foreach ($event in $profitData) {
        Write-Host "- $($event.event_title)"
        Write-Host "  Revenue: $($event.revenue) FCFA"
        Write-Host "  Expenses: $($event.expenses) FCFA" 
        Write-Host "  Profit: $($event.profit) FCFA"
        Write-Host "  Margin: $($event.profit_margin)%"
        Write-Host "  Notes: $($event.notes)"
        Write-Host ""
    }
    
    # Test 3: Get specific event details
    Write-Host "`n--- Test 3: Get Event 2 Details ---"
    $eventResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit/2" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Status: $($eventResponse.StatusCode)"
    $eventData = $eventResponse.Content | ConvertFrom-Json
    Write-Host "Event: $($eventData.event.title)"
    Write-Host "Revenue: $($eventData.profit.revenue) FCFA"
    Write-Host "Expenses: $($eventData.profit.expenses) FCFA"
    Write-Host "Calculated Profit: $($eventData.calculated_profit) FCFA"
    Write-Host "Calculated Margin: $($eventData.calculated_margin.ToString('F2'))%"
    
    Write-Host "`n✅ Backend profit management is working correctly!"
    Write-Host "✅ Data can be retrieved from database"
    Write-Host "✅ Data can be modified and saved to database"
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status: $($_.Exception.Response.StatusCode)"
    }
}
