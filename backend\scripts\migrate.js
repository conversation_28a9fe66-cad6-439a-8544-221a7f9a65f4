const mysql = require("mysql2/promise")
const fs = require("fs")
const path = require("path")
require("dotenv").config()

async function runMigrations() {
  // First connection without database to create the database
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
  })

  try {
    console.log("Running database migrations...")

    // Create database
    await connection.execute("CREATE DATABASE IF NOT EXISTS otantik_ems")
    console.log("✅ Database created successfully!")

    await connection.end()

    // Second connection to the specific database
    const dbConnection = await mysql.createConnection({
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "root",
      password: process.env.DB_PASSWORD || "",
      database: process.env.DB_NAME || "otantik_ems",
    })

    // Read schema file and remove database creation and USE statements
    let schemaSQL = fs.readFileSync(path.join(__dirname, "../database/schema.sql"), "utf8")
    schemaSQL = schemaSQL.replace(/-- Create database[\s\S]*?USE otantik_ems;\s*/i, "")

    // Split SQL into individual statements and execute them one by one
    const statements = schemaSQL.split(';').filter(stmt => stmt.trim().length > 0)

    for (const statement of statements) {
      const trimmedStatement = statement.trim()
      if (trimmedStatement && !trimmedStatement.startsWith('--')) {
        try {
          await dbConnection.execute(trimmedStatement)
        } catch (error) {
          console.error(`Error executing statement: ${trimmedStatement.substring(0, 100)}...`)
          throw error
        }
      }
    }

    await dbConnection.end()

    console.log("✅ Database schema created successfully!")
  } catch (error) {
    console.error("❌ Migration failed:", error)
    process.exit(1)
  }
}

runMigrations()
