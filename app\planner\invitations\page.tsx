"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Send,
  Eye,
  Download,
  Edit,
  MessageSquare,
  Heart,
  Calendar,
  MapPin,
  Clock,
  Users,
  CheckCircle,
  AlertCircle,
} from "lucide-react"
import { PlannerSidebar } from "@/components/planner-sidebar"
import apiClient from "@/lib/api"
import { toast } from "sonner"

const invitationTemplates = [
  {
    id: "traditional",
    name: "Traditional Wedding",
    description: "Elegant design with Cameroon cultural elements",
    colors: { primary: "#D97706", secondary: "#FCD34D", accent: "#92400E" },
    preview: (
      <div className="aspect-[3/4] bg-gradient-to-br from-amber-50 to-yellow-50 border-2 border-amber-200 rounded-lg p-6 text-center relative overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full opacity-10">
          <div
            className="w-full h-full bg-repeat"
            style={{
              backgroundImage:
                'url(\'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="20" fill="%23D97706"/></svg>\')',
            }}
          ></div>
        </div>
        <div className="relative z-10 space-y-4">
          <div className="text-amber-600 text-3xl mb-4">💍</div>
          <h2 className="text-xl font-bold text-amber-800">You're Invited!</h2>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-amber-700">Ngounou Arlane & Otantik Nuna</h3>
            <p className="text-amber-600 text-sm">Wedding Celebration</p>
          </div>
          <div className="space-y-1 text-xs text-amber-600">
            <div className="flex items-center justify-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>December 15, 2024</span>
            </div>
            <div className="flex items-center justify-center gap-1">
              <Clock className="h-3 w-3" />
              <span>6:00 PM</span>
            </div>
            <div className="flex items-center justify-center gap-1">
              <MapPin className="h-3 w-3" />
              <span>Hilton Hotel Yaoundé</span>
            </div>
          </div>
          <div className="pt-2 border-t border-amber-200">
            <p className="text-xs text-amber-500">RSVP by December 1, 2024</p>
          </div>
        </div>
      </div>
    ),
  },
  {
    id: "modern",
    name: "Modern Elegant",
    description: "Clean and sophisticated design",
    colors: { primary: "#1E40AF", secondary: "#3B82F6", accent: "#1E3A8A" },
    preview: (
      <div className="aspect-[3/4] bg-gradient-to-br from-blue-50 to-white border-2 border-blue-200 rounded-lg p-6 text-center">
        <div className="space-y-4">
          <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
            <Heart className="h-8 w-8 text-blue-600" />
          </div>
          <h2 className="text-xl font-bold text-blue-800">Join Us</h2>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-blue-700">Ngounou & Otantik</h3>
            <p className="text-blue-600 text-sm">Wedding Day</p>
          </div>
          <div className="space-y-1 text-xs text-blue-600">
            <p>December 15, 2024</p>
            <p>6:00 PM</p>
            <p>Hilton Hotel Yaoundé</p>
          </div>
          <div className="pt-2">
            <p className="text-xs text-blue-500">Your presence is our gift</p>
          </div>
        </div>
      </div>
    ),
  },
  {
    id: "floral",
    name: "Floral Romance",
    description: "Beautiful floral patterns and romantic colors",
    colors: { primary: "#BE185D", secondary: "#F472B6", accent: "#831843" },
    preview: (
      <div className="aspect-[3/4] bg-gradient-to-br from-pink-50 to-rose-50 border-2 border-pink-200 rounded-lg p-6 text-center relative">
        <div className="absolute top-2 right-2 text-pink-300 text-2xl">🌸</div>
        <div className="absolute bottom-2 left-2 text-pink-300 text-2xl">🌺</div>
        <div className="space-y-4">
          <div className="text-pink-600 text-3xl mb-4">💐</div>
          <h2 className="text-xl font-bold text-pink-800">With Joy</h2>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-pink-700">Ngounou Arlane & Otantik Nuna</h3>
            <p className="text-pink-600 text-sm">Request your presence</p>
          </div>
          <div className="space-y-1 text-xs text-pink-600">
            <p>December 15, 2024</p>
            <p>6:00 PM</p>
            <p>Hilton Hotel Yaoundé</p>
          </div>
          <div className="pt-2">
            <p className="text-xs text-pink-500">Love is in bloom</p>
          </div>
        </div>
      </div>
    ),
  },
  {
    id: "corporate",
    name: "Corporate Event",
    description: "Professional design for business events",
    colors: { primary: "#374151", secondary: "#6B7280", accent: "#111827" },
    preview: (
      <div className="aspect-[3/4] bg-gradient-to-br from-gray-50 to-white border-2 border-gray-200 rounded-lg p-6 text-center">
        <div className="space-y-4">
          <div className="w-16 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center">
            <Users className="h-8 w-8 text-gray-600" />
          </div>
          <h2 className="text-xl font-bold text-gray-800">You're Invited</h2>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-700">TechCorp Conference 2024</h3>
            <p className="text-gray-600 text-sm">Annual Technology Summit</p>
          </div>
          <div className="space-y-1 text-xs text-gray-600">
            <p>November 20, 2024</p>
            <p>9:00 AM</p>
            <p>Palais des Congrès</p>
          </div>
          <div className="pt-2">
            <p className="text-xs text-gray-500">Innovation • Networking • Growth</p>
          </div>
        </div>
      </div>
    ),
  },
]

export default function PlannerInvitations() {
  const [events, setEvents] = useState([])
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [selectedTemplate, setSelectedTemplate] = useState("traditional")
  const [invitationStats, setInvitationStats] = useState({
    totalSent: 0,
    opened: 0,
    responded: 0,
    pending: 0,
  })
  const [isLoading, setIsLoading] = useState(false)
  const [showSendDialog, setShowSendDialog] = useState(false)
  const [showThankYouDialog, setShowThankYouDialog] = useState(false)
  const [invitationMessage, setInvitationMessage] = useState("")
  const [thankYouMessage, setThankYouMessage] = useState("")
  const [invitationImage, setInvitationImage] = useState(null)

  useEffect(() => {
    loadEvents()
    setDefaultMessages()
  }, [])

  const loadEvents = async () => {
    try {
      const eventsData = await apiClient.getEvents()
      setEvents(eventsData)
      if (eventsData.length > 0) {
        setSelectedEvent(eventsData[0])
        loadInvitationStats(eventsData[0].id)
      }
    } catch (error) {
      toast.error("Failed to load events")
    }
  }

  const loadInvitationStats = async (eventId) => {
    try {
      const guests = await apiClient.getGuests(eventId)
      const stats = {
        totalSent: guests.filter((g) => g.whatsapp_sent).length,
        opened: guests.filter((g) => g.rsvp_status !== "pending").length,
        responded: guests.filter((g) => g.rsvp_status === "confirmed" || g.rsvp_status === "declined").length,
        pending: guests.filter((g) => g.rsvp_status === "pending").length,
      }
      setInvitationStats(stats)
    } catch (error) {
      console.error("Failed to load invitation stats:", error)
    }
  }

  const setDefaultMessages = () => {
    setInvitationMessage(`Dear {name},

You are cordially invited to our special celebration!

📅 Date: December 15, 2024
🕕 Time: 6:00 PM  
📍 Venue: Hilton Hotel Yaoundé

Your presence would make our joy complete. Please RSVP by December 1st.

With love and excitement,
Ngounou Arlane & Otantik Nuna

#OtantikEMS`)

    setThankYouMessage(`Dear {name},

Thank you so much for being part of our special day! Your presence made our celebration truly memorable.

We are grateful for the love, joy, and wonderful memories you helped create. 

With heartfelt appreciation,
Ngounou Arlane & Otantik Nuna

#OtantikEMS`)
  }

  const handleSendInvitations = async () => {
    if (!selectedEvent) return

    setIsLoading(true)
    try {
      let imageUrl = null

      // Upload invitation image if provided
      if (invitationImage) {
        const uploadResult = await apiClient.uploadFile(invitationImage, selectedEvent.id, "Invitation Image")
        imageUrl = uploadResult.fileUrl
      }

      const result = await apiClient.sendInvitations(selectedEvent.id, invitationMessage, imageUrl)

      toast.success(`Invitations sent successfully! ${result.successCount}/${result.totalGuests} delivered`)
      setShowSendDialog(false)
      loadInvitationStats(selectedEvent.id)
    } catch (error) {
      toast.error("Failed to send invitations: " + error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSendThankYou = async () => {
    if (!selectedEvent) return

    setIsLoading(true)
    try {
      const result = await apiClient.sendThankYouMessages(selectedEvent.id, thankYouMessage)

      toast.success(`Thank you messages sent! ${result.successCount}/${result.totalGuests} delivered`)
      setShowThankYouDialog(false)
    } catch (error) {
      toast.error("Failed to send thank you messages: " + error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleImageUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      setInvitationImage(file)
    }
  }

  const currentTemplate = invitationTemplates.find((t) => t.id === selectedTemplate) || invitationTemplates[0]

  return (
    <div className="flex h-screen bg-gray-50">
      <PlannerSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Invitations</h1>
              <p className="text-gray-600">Design and send beautiful event invitations via WhatsApp</p>
            </div>
            <div className="flex items-center gap-3">
              <Dialog open={showThankYouDialog} onOpenChange={setShowThankYouDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Send Thank You
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle>Send Thank You Messages</DialogTitle>
                    <DialogDescription>
                      Send thank you messages to all guests who checked in to the event
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="thankYouMessage">Thank You Message</Label>
                      <Textarea
                        id="thankYouMessage"
                        value={thankYouMessage}
                        onChange={(e) => setThankYouMessage(e.target.value)}
                        rows={8}
                        placeholder="Enter your thank you message..."
                      />
                      <p className="text-sm text-gray-500 mt-1">Use {"{name}"} to personalize with guest names</p>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setShowThankYouDialog(false)}>
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSendThankYou}
                      disabled={isLoading}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      {isLoading ? "Sending..." : "Send Thank You Messages"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Dialog open={showSendDialog} onOpenChange={setShowSendDialog}>
                <DialogTrigger asChild>
                  <Button className="bg-gradient-to-r from-blue-600 to-cyan-600">
                    <Send className="h-4 w-4 mr-2" />
                    Send Invitations
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Send WhatsApp Invitations</DialogTitle>
                    <DialogDescription>Send personalized invitations to all guests via WhatsApp</DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="invitationMessage">Invitation Message</Label>
                      <Textarea
                        id="invitationMessage"
                        value={invitationMessage}
                        onChange={(e) => setInvitationMessage(e.target.value)}
                        rows={10}
                        placeholder="Enter your invitation message..."
                      />
                      <p className="text-sm text-gray-500 mt-1">Use {"{name}"} to personalize with guest names</p>
                    </div>
                    <div>
                      <Label htmlFor="invitationImage">Invitation Image (Optional)</Label>
                      <Input
                        id="invitationImage"
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="mt-1"
                      />
                      <p className="text-sm text-gray-500 mt-1">
                        Upload an invitation card image to send with the message
                      </p>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setShowSendDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleSendInvitations} disabled={isLoading}>
                      {isLoading ? "Sending..." : "Send Invitations"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Event Selection */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Select Event</CardTitle>
              <CardDescription>Choose the event to manage invitations for</CardDescription>
            </CardHeader>
            <CardContent>
              <Select
                value={selectedEvent?.id?.toString()}
                onValueChange={(value) => {
                  const event = events.find((e) => e.id.toString() === value)
                  setSelectedEvent(event)
                  if (event) loadInvitationStats(event.id)
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select an event" />
                </SelectTrigger>
                <SelectContent>
                  {events.map((event) => (
                    <SelectItem key={event.id} value={event.id.toString()}>
                      {event.title} - {new Date(event.event_date).toLocaleDateString()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Invitation Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Sent</p>
                    <p className="text-3xl font-bold text-blue-600 mt-2">{invitationStats.totalSent}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Send className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Opened</p>
                    <p className="text-3xl font-bold text-green-600 mt-2">{invitationStats.opened}</p>
                    <p className="text-sm text-green-600 mt-1">
                      {invitationStats.totalSent > 0
                        ? Math.round((invitationStats.opened / invitationStats.totalSent) * 100)
                        : 0}
                      %
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Eye className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Responded</p>
                    <p className="text-3xl font-bold text-amber-600 mt-2">{invitationStats.responded}</p>
                    <p className="text-sm text-amber-600 mt-1">
                      {invitationStats.totalSent > 0
                        ? Math.round((invitationStats.responded / invitationStats.totalSent) * 100)
                        : 0}
                      %
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center">
                    <MessageSquare className="h-6 w-6 text-amber-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-3xl font-bold text-purple-600 mt-2">{invitationStats.pending}</p>
                    <p className="text-sm text-purple-600 mt-1">
                      {invitationStats.totalSent > 0
                        ? Math.round((invitationStats.pending / invitationStats.totalSent) * 100)
                        : 0}
                      %
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <AlertCircle className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Invitation Templates */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Invitation Templates</CardTitle>
              <CardDescription>Choose and customize your invitation design</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                {invitationTemplates.map((template) => (
                  <div
                    key={template.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${
                      selectedTemplate === template.id ? "ring-2 ring-blue-500 bg-blue-50" : ""
                    }`}
                    onClick={() => setSelectedTemplate(template.id)}
                  >
                    {template.preview}
                    <div className="mt-4">
                      <h3 className="font-semibold text-sm">{template.name}</h3>
                      <p className="text-xs text-gray-600 mt-1">{template.description}</p>
                      <div className="flex items-center justify-between mt-3">
                        <Badge
                          className={
                            selectedTemplate === template.id ? "bg-blue-100 text-blue-800" : "bg-gray-100 text-gray-800"
                          }
                        >
                          {selectedTemplate === template.id ? "Selected" : "Available"}
                        </Badge>
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Current Invitation Preview */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Current Invitation Preview</CardTitle>
                  <CardDescription>Preview of the selected invitation template</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                  <Button variant="outline">
                    <Edit className="h-4 w-4 mr-2" />
                    Customize
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="max-w-sm mx-auto">{currentTemplate.preview}</div>
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600">
                  This invitation will be sent via WhatsApp to all guests with phone numbers
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
