const mysql = require("mysql2/promise")
const bcrypt = require("bcryptjs")
require("dotenv").config()

async function seedDatabase() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: "otantik_ems",
  })

  try {
    console.log("Seeding database with sample data...")

    // Hash password for sample users
    const hashedPassword = await bcrypt.hash("password123", 10)

    // Insert sample admin user
    await connection.execute(`
      INSERT INTO users (first_name, last_name, email, password, role, region, phone) VALUES
      (?, ?, ?, ?, ?, ?, ?)
    `, ['Admin', 'User', '<EMAIL>', hashedPassword, 'admin', 'Centre Region', '+237 6XX XXX XXX'])

    // Insert sample planners
    await connection.execute(`
      INSERT INTO users (first_name, last_name, email, password, role, region, phone) VALUES
      (?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?)
    `, [
      'Bimm', 'Audrey', '<EMAIL>', hashedPassword, 'planner', 'Centre Region', '+237 6XX XXX XXX',
      'Tantoh', 'Emmanuel', '<EMAIL>', hashedPassword, 'planner', 'Northwest Region', '+237 6XX XXX XXX'
    ])

    // Insert sample vendors
    await connection.execute(`
      INSERT INTO vendors (business_name, contact_person, email, phone, service_type, region, address, description, rating, total_events, status) VALUES
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'Ange Photography', 'Ange Marie', '<EMAIL>', '+237 6XX XXX XXX', 'photographer', 'Centre Region', 'Yaoundé, Centre Region', 'Professional wedding and event photography', 4.8, 25, 'active',
      'Marie Catering Services', 'Marie Fotso', '<EMAIL>', '+237 6XX XXX XXX', 'caterer', 'Littoral Region', 'Douala, Littoral Region', 'Traditional and modern catering services', 4.6, 18, 'active',
      'Tantoh Event Decorations', 'Tantoh Brice', '<EMAIL>', '+237 6XX XXX XXX', 'decorator', 'Northwest Region', 'Bamenda, Northwest Region', 'Creative event decoration and design', 4.9, 32, 'active'
    ])

    // Insert sample events
    await connection.execute(`
      INSERT INTO events (title, description, event_date, event_time, venue, address, region, expected_guests, budget, planner_id, status) VALUES
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'Ngounou Arlane & Otantik Nuna Wedding', 'Traditional wedding celebration with modern touches', '2024-12-15', '18:00:00', 'Hilton Hotel Yaoundé', 'Boulevard du 20 Mai, Yaoundé', 'Centre Region', 300, 15000000.00, 2, 'confirmed',
      'Ange & Marie Traditional Wedding', 'Beautiful traditional Cameroonian wedding', '2024-12-20', '16:00:00', 'Cultural Center Douala', 'Akwa, Douala', 'Littoral Region', 400, 25000000.00, 3, 'planning',
      'Tech Conference Cameroon 2024', 'Annual technology and innovation summit', '2024-12-25', '09:00:00', 'Palais des Congrès Yaoundé', 'Centre Ville, Yaoundé', 'Centre Region', 800, 50000000.00, 2, 'confirmed'
    ])

    // Insert sample guests for the first event
    await connection.execute(`
      INSERT INTO guests (event_id, name, email, phone, table_number, seat_number, dietary_requirements, plus_one, rsvp_status) VALUES
      (?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      1, 'Ngounou Arlane', '<EMAIL>', '+237 6XX XXX XXX', 'Table 5', 'Seat 3', 'None', true, 'confirmed',
      1, 'Otantik Nuna', '<EMAIL>', '+237 6XX XXX XXX', 'Table 5', 'Seat 4', 'Vegetarian', false, 'confirmed',
      1, 'Ange Marie', '<EMAIL>', '+237 6XX XXX XXX', 'Table 8', 'Seat 2', 'None', true, 'pending',
      1, 'Tantoh Emmanuel', '<EMAIL>', '+237 6XX XXX XXX', 'Table 12', 'Seat 1', 'Halal', false, 'confirmed'
    ])

    await connection.end()
    console.log("✅ Database seeded successfully!")

  } catch (error) {
    console.error("❌ Seeding failed:", error)
    process.exit(1)
  }
}

seedDatabase()
