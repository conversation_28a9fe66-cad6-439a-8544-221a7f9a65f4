$loginBody = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

$loginResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json" -UseBasicParsing
$token = ($loginResponse.Content | ConvertFrom-Json).token

$headers = @{
    "Authorization" = "Bearer $token"
}

Write-Host "Testing profit summary endpoint..."
try {
    $summaryResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/profit/summary" -Method GET -Headers $headers -UseBasicParsing
    Write-Host "Status: $($summaryResponse.StatusCode)"
    Write-Host "Response: $($summaryResponse.Content)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Status: $($_.Exception.Response.StatusCode)"
}
