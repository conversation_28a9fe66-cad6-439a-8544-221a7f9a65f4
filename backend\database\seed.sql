-- Insert sample admin user
INSERT INTO users (first_name, last_name, email, password, role, region, phone) VALUES
('Admin', 'User', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'Centre Region', '+237 6XX XXX XXX');

-- Insert sample planners
INSERT INTO users (first_name, last_name, email, password, role, region, phone) VALUES
('Bimm', 'Audrey', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'planner', 'Centre Region', '+237 6XX XXX XXX'),
('Tantoh', 'Emmanuel', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'planner', 'Northwest Region', '+237 6XX XXX XXX');

-- Insert sample vendors
INSERT INTO vendors (business_name, contact_person, email, phone, service_type, region, address, description, rating, total_events, status) VALUES
('Ange Photography', 'Ange Marie', '<EMAIL>', '+237 6XX XXX XXX', 'photographer', 'Centre Region', 'Yaoundé, Centre Region', 'Professional wedding and event photography', 4.8, 25, 'active'),
('Marie Catering Services', 'Marie Fotso', '<EMAIL>', '+237 6XX XXX XXX', 'caterer', 'Littoral Region', 'Douala, Littoral Region', 'Traditional and modern catering services', 4.6, 18, 'active'),
('Tantoh Event Decorations', 'Tantoh Brice', '<EMAIL>', '+237 6XX XXX XXX', 'decorator', 'Northwest Region', 'Bamenda, Northwest Region', 'Creative event decoration and design', 4.9, 32, 'active');

-- Insert sample events
INSERT INTO events (title, description, event_date, event_time, venue, address, region, expected_guests, budget, planner_id, status) VALUES
('Ngounou Arlane & Otantik Nuna Wedding', 'Traditional wedding celebration with modern touches', '2024-12-15', '18:00:00', 'Hilton Hotel Yaoundé', 'Boulevard du 20 Mai, Yaoundé', 'Centre Region', 300, 15000000.00, 2, 'confirmed'),
('Ange & Marie Traditional Wedding', 'Beautiful traditional Cameroonian wedding', '2024-12-20', '16:00:00', 'Cultural Center Douala', 'Akwa, Douala', 'Littoral Region', 400, 25000000.00, 3, 'planning'),
('Tech Conference Cameroon 2024', 'Annual technology and innovation summit', '2024-12-25', '09:00:00', 'Palais des Congrès Yaoundé', 'Centre Ville, Yaoundé', 'Centre Region', 800, 50000000.00, 2, 'confirmed');

-- Insert sample guests for the first event
INSERT INTO guests (event_id, name, email, phone, table_number, seat_number, dietary_requirements, plus_one, rsvp_status) VALUES
(1, 'Ngounou Arlane', '<EMAIL>', '+237 6XX XXX XXX', 'Table 5', 'Seat 3', 'None', TRUE, 'confirmed'),
(1, 'Otantik Nuna', '<EMAIL>', '+237 6XX XXX XXX', 'Table 5', 'Seat 4', 'Vegetarian', FALSE, 'confirmed'),
(1, 'Ange Marie', '<EMAIL>', '+237 6XX XXX XXX', 'Table 8', 'Seat 2', 'None', TRUE, 'pending'),
(1, 'Tantoh Emmanuel', '<EMAIL>', '+237 6XX XXX XXX', 'Table 12', 'Seat 1', 'Halal', FALSE, 'confirmed');

-- Assign vendors to events
INSERT INTO event_vendors (event_id, vendor_id, role, status) VALUES
(1, 1, 'Wedding Photographer', 'confirmed'),
(1, 2, 'Catering Service', 'confirmed'),
(2, 1, 'Event Photographer', 'assigned'),
(3, 3, 'Event Decorator', 'confirmed');

-- Insert sample notifications
INSERT INTO notifications (user_id, title, message, type) VALUES
(2, 'New Event Created', 'Your event "Ngounou Arlane & Otantik Nuna Wedding" has been created successfully.', 'success'),
(2, 'Guest RSVP Update', 'Ngounou Arlane has confirmed attendance for your event.', 'info'),
(3, 'Vendor Assignment', 'You have been assigned to "Ange & Marie Traditional Wedding".', 'info');
