"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Users, Plus, Search, Filter, Mail, Phone, MoreHorizontal, Upload } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { PlannerSidebar } from "@/components/planner-sidebar"
import { guestService } from "@/lib/services/guest.service"

interface Guest {
  id: number
  name: string
  email: string
  phone: string
  rsvp: string
  table: string
  dietary: string
  plusOne: boolean
  checkedIn: boolean
}

export default function PlannerGuests() {
  const [guests, setGuests] = useState<Guest[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [rsvpFilter, setRsvpFilter] = useState("all")

  useEffect(() => {
    const fetchGuests = async () => {
      try {
        setLoading(true)
        const data = await guestService.getAllGuests()
        setGuests(data)
      } catch (error) {
        console.error("Failed to fetch guests:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchGuests()
  }, [])

  const filteredGuests = guests.filter((guest) => {
    const matchesSearch =
      guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRsvp = rsvpFilter === "all" || guest.rsvp === rsvpFilter
    return matchesSearch && matchesRsvp
  })

  const getRsvpBadge = (rsvp: string) => {
    switch (rsvp) {
      case "confirmed":
        return <Badge className="bg-green-100 text-green-800">Confirmed</Badge>
      case "pending":
        return <Badge className="bg-amber-100 text-amber-800">Pending</Badge>
      case "declined":
        return <Badge className="bg-red-100 text-red-800">Declined</Badge>
      default:
        return <Badge variant="outline">{rsvp}</Badge>
    }
  }

  const handleCreateGuest = async (guestData: any) => {
    try {
      const newGuest = await guestService.createGuest(guestData)
      setGuests([...guests, newGuest])
    } catch (error) {
      console.error("Failed to create guest:", error)
    }
  }

  const handleUpdateRsvp = async (guestId: number, rsvp: string) => {
    try {
      await guestService.updateRsvp(guestId, rsvp)
      setGuests(guests.map((guest) => (guest.id === guestId ? { ...guest, rsvp } : guest)))
    } catch (error) {
      console.error("Failed to update RSVP:", error)
    }
  }

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <PlannerSidebar />
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <PlannerSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Guest Management</h1>
              <p className="text-gray-600">Manage guest list and RSVPs</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Import CSV
              </Button>
              <Button className="bg-gradient-to-r from-blue-600 to-cyan-600">
                <Plus className="h-4 w-4 mr-2" />
                Add Guest
              </Button>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Guests</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">{guests.length}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Confirmed</p>
                    <p className="text-3xl font-bold text-green-600 mt-2">
                      {guests.filter((g) => g.rsvp === "confirmed").length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-3xl font-bold text-amber-600 mt-2">
                      {guests.filter((g) => g.rsvp === "pending").length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-amber-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Checked In</p>
                    <p className="text-3xl font-bold text-purple-600 mt-2">
                      {guests.filter((g) => g.checkedIn).length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Guests Table */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    Guest List
                  </CardTitle>
                  <CardDescription>Manage all event guests and their details</CardDescription>
                </div>
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search guests..."
                      className="pl-10 w-80"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline">
                        <Filter className="h-4 w-4 mr-2" />
                        RSVP: {rsvpFilter === "all" ? "All" : rsvpFilter}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => setRsvpFilter("all")}>All Status</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setRsvpFilter("confirmed")}>Confirmed</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setRsvpFilter("pending")}>Pending</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setRsvpFilter("declined")}>Declined</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Guest</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>RSVP Status</TableHead>
                      <TableHead>Table</TableHead>
                      <TableHead>Dietary</TableHead>
                      <TableHead>Plus One</TableHead>
                      <TableHead>Check-in</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredGuests.map((guest) => (
                      <TableRow key={guest.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarFallback>
                                {guest.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{guest.name}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-1 text-sm">
                              <Mail className="h-3 w-3 text-gray-500" />
                              {guest.email}
                            </div>
                            <div className="flex items-center gap-1 text-sm">
                              <Phone className="h-3 w-3 text-gray-500" />
                              {guest.phone}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{getRsvpBadge(guest.rsvp)}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{guest.table}</Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{guest.dietary}</span>
                        </TableCell>
                        <TableCell>
                          {guest.plusOne ? (
                            <Badge className="bg-blue-100 text-blue-800">Yes</Badge>
                          ) : (
                            <Badge variant="secondary">No</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {guest.checkedIn ? (
                            <Badge className="bg-green-100 text-green-800">Checked In</Badge>
                          ) : (
                            <Badge variant="secondary">Not Arrived</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>Edit Guest</DropdownMenuItem>
                              <DropdownMenuItem>Send Invitation</DropdownMenuItem>
                              <DropdownMenuItem>Assign Table</DropdownMenuItem>
                              <DropdownMenuItem>Generate QR Code</DropdownMenuItem>
                              <DropdownMenuItem className="text-red-600">Remove Guest</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
